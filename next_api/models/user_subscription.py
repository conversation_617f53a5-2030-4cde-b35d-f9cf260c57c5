from enum import IntEnum, StrEnum
from tortoise.models import Model
from tortoise.fields import (
    UUIDField,
    ForeignKeyField,
    SmallIntField,
    DatetimeField,
    ForeignKeyRelation,
    TextField
)
from uuid_extensions import uuid7


class CancellationReason(StrEnum):
    """Enum for subscription cancellation reasons."""
    TOO_EXPENSIVE = "too_expensive"
    MISSING_FEATURES = "missing_features"
    SWITCHED_SERVICE = "switched_service"
    UNUSED = "unused"
    CUSTOMER_SERVICE = "customer_service"
    LOW_QUALITY = "low_quality"
    TOO_COMPLEX = "too_complex"
    OTHER = "other"


class UserSubscription(Model):
    class Meta:
        table = "user_subscription"
        schema = "public"
        ordering = ["created_at"]

    class Status(IntEnum):
        INACTIVE = 0
        ACTIVE = 1
        EXPIRED = 2
        CANCELLED = 3
        PENDING = 4

    id = UUIDField(pk=True, default=uuid7)
    partner = TextField(null=True)
    partner_id = TextField(null=True)
    status = SmallIntField(null=False, default=Status.INACTIVE)
    start_date = DatetimeField(null=False)
    end_date = DatetimeField(null=True)
    last_billing_date = DatetimeField(null=True)
    next_billing_date = DatetimeField(null=True)
    cancellation_reason = TextField(null=True)
    cancellation_comment = TextField(null=True)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)

    user: ForeignKeyRelation["User"] = ForeignKeyField(
        "next_api.User",
        related_name="subscriptions"
    )
    subscription_plan: ForeignKeyRelation["SubscriptionPlan"] = ForeignKeyField(
        "next_api.SubscriptionPlan",
        related_name="subscribers"
    )
