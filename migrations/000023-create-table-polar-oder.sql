-- DROP TABLE IF EXISTS "polar_order";
CREATE TABLE IF NOT EXISTS "polar_order" (
    id UUID PRIMARY KEY,
    user_id UUID,
    user_email TEXT,
    order_id TEXT,
    product_id TEXT,
    subscription_plan_id UUID,
    currency TEXT DEFAULT 'USD',
    subtotal_amount DECIMAL(18, 2) DEFAULT 0,
    discount_amount DECIMAL(18, 2) DEFAULT 0,
    net_amount DECIMAL(18, 2) DEFAULT 0,
    amount DECIMAL(18, 2) DEFAULT 0,
    tax_amount DECIMAL(18, 2) DEFAULT 0,
    total_amount DECIMAL(18, 2) DEFAULT 0,
    refunded_amount DECIMAL(18, 2) DEFAULT 0,
    refunded_tax_amount DECIMAL(18, 2) DEFAULT 0,
    paid BOOLEAN,
    status SMALLINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);