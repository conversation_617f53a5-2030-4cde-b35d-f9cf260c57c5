import io
import re
import json
import os
import types
import string
import random
import base64
from typing import Any
from typing import Optional

from sanic.log import logger
from pydantic import BaseModel, Field
from resume_parser.CFG import CFG, ResumeSchema
from groq import Groq
from uuid_extensions import uuid7
import docx
import fitz
import pdfplumber
import openai
from utils import llm_helpers
from PIL import Image

import os

class ResumeJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder that can handle mappingproxy objects and other special types."""
    def default(self, obj):
        if obj == "":
            return "N/A"
        if obj is None:
            return "N/A"
        if isinstance(obj, types.MappingProxyType):
            # Convert mappingproxy to dict
            return dict(obj)
        elif isinstance(obj, type):
            # If it's a class, return its name
            return obj.__name__
        elif hasattr(obj, "__dict__"):
            # For objects with __dict__, convert to dict
            return {k: v for k, v in obj.__dict__.items()
                   if not k.startswith("_")}
        elif isinstance(obj, (list, tuple)):
            # For lists and tuples
            return [item for item in obj]
        # Let the base class handle other types or raise TypeError
        return super().default(obj)


def clean_text(pdf_str: str):
    pdf_str = re.sub(r'\s[,.]', ',', pdf_str)
    # pdf_str = re.sub(r'[\n]+', '\n', pdf_str)
    pdf_str = re.sub(r'[\s]+', ' ', pdf_str)
    pdf_str = re.sub(r'http[s]?(://)?', '', pdf_str)
    pdf_str = re.sub(r'[\u0080-\uffff]', '', pdf_str)
    return pdf_str


def find_matching_words(pdf_content: str):
    word_collections = [
        'experiences', 'experience', 'summary', 'profile',
        'school', 'university', 'college', 'education',
        'references', 'reference',
        "skill", "skills", "language",
    ]
    matched_words = []
    for word in word_collections:
        pattern = re.compile(r'\b{}\b'.format(re.escape(word)), re.IGNORECASE)
        if re.search(pattern, pdf_content):
            matched_words.append(word)
    return matched_words


class PDFReader(BaseModel):
    need_clean: bool = True

    def read(self, resume_file, fast=False):
        text = ""
        try:
            if fast:
                text = self.extract_text_from_pdf(resume_file)
            else:
                text = self.read_pdf(resume_file)

        except Exception as e:
            print(f"Can't read file {resume_file.name}, cause: {str(e)}")
            return ""

        if self.need_clean:
            text = clean_text(text)

        return text

    def read_pdf(self, file):
        output_string = io.StringIO()
        with io.BytesIO(file) as f:
            # Open PDF with pdfplumber
            with pdfplumber.open(f) as pdf:
                for page in pdf.pages:
                    # Extract text from each page
                    page_text = page.extract_text(keep_blank_chars=True)
                    output_string.write(page_text)
                    output_string.write("\n---PAGE---\n")

        return output_string.getvalue()

    def extract_text_from_pdf(self, file_bytes):
        """Extract raw text content from pdf file

        Args:
            filename (_type_): _description_
        """
        pdf = fitz.open(stream=file_bytes, filetype="pdf")
        cv_text = ""
        for page in pdf:
            cv_text += page.get_text() + '\n'

        return cv_text

    def extract_text_from_doc(self, file):
        document = docx.Document(file)
        text = ""
        for p in document.paragraphs:
            text += "\n" + p.text

        return text

# Add UUID7 to every item in the JSON that has an 'id' field
def add_uuid_to_items(data):
    if isinstance(data, dict):
        # If this is a dict with an 'id' field that needs a UUID
        if 'id' in data and (data['id'] == '' or data['id'] is None):
            data['id'] = str(uuid7())

        # Process all values in the dict
        for key, value in data.items():
            try:
                data[key] = add_uuid_to_items(value)
            except (TypeError, AttributeError) as e:
                # Log the error but continue processing
                logger.warning(f"Error processing item {key}: {str(e)}")
                # Keep the original value if processing fails
                continue

        return data
    elif isinstance(data, list):
        # Process all items in the list
        try:
            return [add_uuid_to_items(item) for item in data]
        except (TypeError, AttributeError) as e:
            logger.warning(f"Error processing list: {str(e)}")
            # Return original list if processing fails
            return data
    else:
        # Return primitive values as is
        return data

class ParserEngine(BaseModel):
    openai_api_key: str
    groq_api_key: str
    org: Optional[str] = None
    groq_model: str = llm_helpers.get_available_groq_model('llama-3.3-70b-versatile')
    general_description: str = (
        "Extract the resume info (contain information of worker) "
        "below into only a JSON"
        "with exactly the following format (If any fields cannot be found in the resume, "
        "please leave them default value). Do not return empty objects in the JSON."
    )
    prompt_basic_info: str = CFG.prompt_basic_info
    new_prompt_basic_info: str = CFG.new_prompt_basic_info
    # prompt_relation_info: str = CFG.prompt_relation_info
    # function_calling: list = CFG.function_calling
    resume_schema: BaseModel = Field(default_factory=lambda: ResumeSchema())
    # Check for missing required fields and add default values
    required_fields: dict = {
        "basics": {
            "fullname": "",
            "headline": "",
            "email": "",
            "phone": "",
            "location": "",
            "url": {"label": "", "href": ""},
            "customFields": [],
            "picture": {
                "url": "https://i.imgur.com/HgwyOuJ.jpg",
                "size": 64,
                "aspectRatio": 1,
                "borderRadius": 0,
                "effects": {
                    "hidden": False,
                    "border": False,
                    "grayscale": False
                }
            }
        },
        "sections": {
            "summary": {
                "name": "Summary",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "summary",
                "content": "Next Career is a platform that helps you achieve your dream job."
            },
            "profiles": {
                "name": "Profiles",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "profiles",
                "items": [
                    {
                        "id": ''.join(random.choices(string.ascii_letters + string.digits, k=24)),
                        "visible": True,
                        "network": "linkedin",
                        "icon": "linkedin",
                        "username": "",
                        "url": {
                            "label": "",
                            "href": "https://linkedin.com"
                        }
                    }
                ]
            },
            "skills": {
                "name": "Skills",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "skills",
                "items": []
            },
            "education": {
                "name": "Education",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "education",
                "items": []
            },
            "experience": {
                "name": "Experience",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "experience",
                "items": []
            },
            "awards": {
                "name": "Awards",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "awards",
                "items": [
                ]
            },
            "projects": {
                "name": "Projects",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "projects",
                "items": []
            },
            "volunteer": {
                "name": "Volunteering",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "volunteer",
                "items": [
                ]
            },
            "interests": {
                "name": "Interests",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "interests",
                "items": [
                ]
            },
            "certifications": {
                "name": "Certifications",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "certifications",
                "items": [
                ]
            },
            "references": {
                "name": "References",
                "columns": 1,
                "separateLinks": True,
                "visible": False,
                "id": "references",
                "items": [
                ]
            },
            "languages": {
                "name": "Languages",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "languages",
                "items": [
                ]
            },
            "publications": {
                "name": "Publications",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "publications",
                "items": [
                ]
            },
            "custom": {}
        },
        "metadata": {
            "template": "gengar",
            "layout": [
                [
                    ["profiles", "summary", "experience", "volunteer", "references", "projects"],
                    ["skills", "education", "interests", "certifications", "awards", "publications", "languages"]
                ]
            ],
            "css": {
                "value": "",
                "visible": False
            },
            "page": {
                "margin": 12,
                "format": "letter",
                "options": {
                    "breakLine": True,
                    "pageNumbers": True
                }
            },
            "theme": {
                "background": "#ffffff",
                "text": "#000000",
                "primary": "#57534e"
            },
            "typography": {
                "font": {
                    "family": "Lora",
                    "subset": "latin",
                    "variants": ["regular"],
                    "size": 12
                }
            }
        }
    }

    def __init__(self, **data: Any) -> None:
        super().__init__(**data)
        openai.api_key = self.openai_api_key

        if self.org:
            openai.organization = self.org
        # del self.openai_api_key, self.org

    def word_count(self, text: str) -> int:
        """
        Returns the total number of words in the given text.
        """
        words = text.split()
        return len(words)

    def pdf2string(self: object, pdf_contents: bytes) -> str:
        # pdf2text
        pdf_reader = PDFReader(need_clean=True)
        cv_content = pdf_reader.read(pdf_contents, fast=False)
        return cv_content

    # Helper function to check and replace nextcareer.ai values
    def replace_nextcareer_value(self, value):
        if isinstance(value, str) and value == "https://nextcareer.ai":
            return " "
        if isinstance(value, str) and "Next Career is a platform that helps you achieve your dream job." in value:
            return " "
        return value

    def ensure_required_fields(self, data, template, path=""):
        if isinstance(template, dict) and isinstance(data, dict):
            for key, default_value in template.items():
                current_path = f"{path}.{key}" if path else key

                # If key doesn't exist in data, add it with default value
                if key not in data or data[key] is None:
                    logger.info(f"Adding missing required field: {current_path}")
                    data[key] = default_value
                    # Set visible to false for default values
                    if isinstance(default_value, dict) and 'visible' in default_value:
                        default_value['visible'] = False

                # Replace nextcareer.ai values
                if isinstance(data[key], str) and key != "href":
                    data[key] = self.replace_nextcareer_value(data[key])

                # If key exists but is a container type, recurse
                elif isinstance(default_value, (dict, list)) and isinstance(data[key], (dict, list)):
                    self.ensure_required_fields(data[key], default_value, current_path)

        elif isinstance(template, list) and isinstance(data, list):
            # For lists, we don't add default items, but we ensure each item has required structure
            # Check if template has items and the first item is a dict (template for list items)
            if template and len(template) > 0 and isinstance(template[0], dict):
                template_item = template[0]
                for i, item in enumerate(data):
                    if isinstance(item, dict):
                        # Ensure 'visible' field exists
                        if 'visible' not in item:
                            logger.info(f"Adding missing 'visible' field to item: {path}[{i}]")
                            item['visible'] = True

                        # Ensure 'url' field exists
                        if 'url' not in item:
                            logger.info(f"Adding missing 'url' field to item: {path}[{i}]")
                            item['url'] = {
                                "label": "",
                                "href": "",
                                "visible": False
                            }

                        # Replace nextcareer.ai values in dict items
                        for key, value in item.items():
                            if key == "url":
                                if isinstance(value, dict) and "href" in value:
                                    if value["href"] == "https://nextcareer.ai":
                                        value["visible"] = False
                                        value["href"]=""
                                    elif len(value["href"]) > 0 and not value["href"].startswith("https"):
                                        value["visible"] = False
                                        value["href"] = "https://." + value["href"]
                                # Ensure url has visible field
                                if isinstance(value, dict) and "visible" not in value:
                                    value["visible"] = False
                            elif isinstance(value, str) and key != "href":
                                item[key] = self.replace_nextcareer_value(value)
                            elif isinstance(value, dict):
                                for sub_key, sub_value in value.items():
                                    if isinstance(sub_value, str) and sub_key != "href":
                                        value[sub_key] = self.replace_nextcareer_value(sub_value)

                        # Special handling for volunteer items - ensure organization is not empty
                        if path == "sections.volunteer.items":
                            if "organization" in item and (item["organization"] == "" or item["organization"] is None):
                                item["organization"] = "Organization"
                            if "organization" not in item:
                                item["organization"] = "Organization"
                            if "location" in item and (item["location"] == "" or item["location"] is None):
                                item["location"] = "N/A"
                        if path == "sections.education.items":
                            education_items = ('score', 'date', 'institution', 'studyType', 'area', 'summary')
                            for key in education_items:
                                if key in item and (item[key] == "" or item[key] is None):
                                    item[key] = "N/A"
                                if key not in item:
                                    item[key] = "N/A"
                        # Ensure projects.items.name is not null, empty, or unset
                        if path == "sections.projects.items":
                            if "name" not in item or item["name"] is None or (isinstance(item["name"], str) and item["name"].strip() == ""):
                                item["name"] = "Project"

                        self.ensure_required_fields(item, template_item, f"{path}[{i}]")
                        # Check if item matches default values
                        is_default = True
                        for key, value in template_item.items():
                            if key not in item or item[key] != value:
                                is_default = False
                                break
                        # Set visible to false for default items
                        if is_default and 'visible' in item:
                            item['visible'] = False
            # For non-dict template lists (like layout arrays), skip processing to avoid string indexing issues
            elif template and not isinstance(template[0], dict):
                logger.debug(f"Skipping list processing for non-dict template at path: {path}")

        if path == "basics" and isinstance(data, dict) and isinstance(template, dict):
            # Ensure url exists in basics
            if 'url' not in data:
                data['url'] = {
                    "label": "",
                    "href": "",
                    "visible": False
                }

            if 'url' in data:
                # Ensure url has visible field
                if 'visible' not in data['url']:
                    data['url']['visible'] = False

                if 'href' in data['url']:
                    if data['url']['href'] == "https://nextcareer.ai":
                        data['url']['href']=""
                        data['url']['visible'] = False
                if 'label' in data['url']:
                    data['url']['label'] = self.replace_nextcareer_value(data['url']['label'])

        # Special handling for sections to ensure all required section objects exist
        if path == "sections" and isinstance(data, dict) and isinstance(template, dict):
            required_sections = [
                "awards",
                "volunteer",
                "interests",
                "languages",
                "publications",
                "references",
                "certifications",
                "projects",
                "experience",
                "skills",
                "education"
            ]

            if 'summary' in data and isinstance(data['summary'], dict) and 'columns' in data['summary'] and data['summary']['columns'] <= 0:
                data['summary']['columns'] = 1

            for section in required_sections:
                # Ensure section exists as a dict
                if section not in data:
                    logger.info(f"Adding missing section: {section}")
                    if section in template and isinstance(template[section], dict):
                        data[section] = template[section].copy()
                    else:
                        data[section] = {"items": []}
                
                # Ensure it's a dict
                if not isinstance(data[section], dict):
                    data[section] = {"items": []}
                
                if 'columns' in data[section]:
                    # Ensure columns is always > 0
                    if data[section]['columns'] <= 0:
                        data[section]['columns'] = 1
                        
                if 'items' not in data[section] or data[section]['items'] == []:
                    logger.info(f"Adding missing required section items: {section}")
                    if section in template and isinstance(template[section], dict) and 'items' in template[section]:
                        data[section]['items'] = template[section]['items']
                        # Set visible to false for default items
                        for item in data[section]['items']:
                            if isinstance(item, dict) and 'visible' in item:
                                item['visible'] = False
                            # Ensure url exists
                            if isinstance(item, dict) and 'url' not in item:
                                item['url'] = {
                                    "label": "",
                                    "href": "",
                                    "visible": False
                                }
                            # Replace nextcareer.ai values in items
                            if isinstance(item, dict):
                                for key, value in item.items():
                                    if isinstance(value, str) and key != "href":
                                        item[key] = self.replace_nextcareer_value(value)
                                    elif isinstance(value, dict):
                                        for sub_key, sub_value in value.items():
                                            if isinstance(sub_value, str) and sub_key != "href":
                                                value[sub_key] = self.replace_nextcareer_value(sub_value)
                                    if key == "url":
                                        # Ensure url has visible field
                                        if isinstance(value, dict) and "visible" not in value:
                                            value["visible"] = False
                                        if isinstance(value, dict) and "href" in value:
                                            if value["href"] == "https://nextcareer.ai":
                                                value["visible"] = False
                                                value["href"]=""
                        # Ensure projects.items.name is not null, empty, or unset
                        if section == "projects":
                            for item in data[section]['items']:
                                if isinstance(item, dict) and ("name" not in item or item["name"] is None or (isinstance(item["name"], str) and item["name"].strip() == "")):
                                    item["name"] = "Project"
                    else:
                        data[section]['items'] = []
                
                # Process existing items
                if isinstance(data[section]['items'], list):
                    for item in data[section]['items']:
                        if isinstance(item, dict):
                            # Ensure visible field exists
                            if 'visible' not in item:
                                item['visible'] = True

                            # Ensure url exists
                            if 'url' not in item:
                                item['url'] = {
                                    "label": "",
                                    "href": "",
                                    "visible": False
                                }

                            # Special handling for volunteer items - ensure organization is not empty
                            if section == "volunteer":
                                if "organization" in item and (item["organization"] == "" or item["organization"] is None):
                                    item["organization"] = "Organization"
                                if "location" in item and (item["location"] == "" or item["location"] is None):
                                    item["location"] = "N/A"
                            if section == "experience":
                                if "company" in item and (item["company"] == "" or item["company"] is None):
                                    item["company"] = "Company"
                            if section == "profiles":
                                if "username" not in item:
                                    item["username"] = ""
                                if "name" not in item:
                                    item["name"] = "Profile"
                                if "network" not in item:
                                    item["network"] = "linkedin"
                                if "icon" not in item:
                                    item["icon"] = "linkedin"
                            # Ensure projects.items.name is not null, empty, or unset
                            if section == "projects":
                                if "name" not in item or item["name"] is None or (isinstance(item["name"], str) and item["name"].strip() == ""):
                                    item["name"] = "Project"

                            # Replace nextcareer.ai values in items
                            for key, value in item.items():
                                if isinstance(value, str) and key != "href":
                                    item[key] = self.replace_nextcareer_value(value)
                                elif isinstance(value, dict):
                                    for sub_key, sub_value in value.items():
                                        if isinstance(sub_value, str) and sub_key != "href":
                                            value[sub_key] = self.replace_nextcareer_value(sub_value)
                                        if sub_key == "url":
                                            # Ensure url has visible field
                                            if isinstance(value, dict) and "visible" not in value:
                                                value["visible"] = False
                                            if isinstance(value, dict) and "href" in value:
                                                if value["href"] == "https://nextcareer.ai":
                                                    value["visible"] = False
                                                    value["href"]=""

    def query_resume_langchain(
        self: object, pdf_contents: str
    ) -> dict:
        groq_client: Groq = Groq(api_key=self.groq_api_key)
        
        # Enhanced system prompt for comprehensive extraction
        enhanced_system_prompt = f"""
        {self.new_prompt_basic_info}
        
        ADDITIONAL CRITICAL INSTRUCTIONS FOR MARKDOWN INPUT:
        
        1. You are receiving MARKDOWN-FORMATTED text that contains ALL extracted resume information
        2. This markdown may contain headers (# ## ###), bullet points (-), lists, and formatted text
        3. Parse through EVERY line and section of the markdown content
        4. Extract information from ALL markdown sections including:
           - Headers that indicate section names (Experience, Education, Skills, etc.)
           - Bullet points that contain detailed information
           - Any formatted text, dates, locations, descriptions
           - All technical terms, company names, job titles, achievements
           - All contact information, links, and references
        
        5. COMPREHENSIVE EXTRACTION REQUIREMENTS:
           - Read through the ENTIRE markdown content line by line
           - Do not skip any sections or subsections
           - Extract ALL work experiences, even brief ones
           - Extract ALL educational qualifications and courses
           - Extract ALL skills mentioned anywhere in the text
           - Extract ALL projects, achievements, certifications
           - Extract ALL dates, locations, and specific details
           - Extract ALL contact information and links
        
        6. CONTENT PRESERVATION:
           - Keep original wording and descriptions intact
           - Preserve all technical terms and industry language
           - Maintain all specific metrics, numbers, and achievements
           - Include all company names, job titles, and locations exactly as written
           - Preserve all date formats and ranges
        
        7. SECTION MAPPING:
           - Map markdown headers to appropriate JSON sections
           - Distribute information to correct schema fields
           - Ensure no information is lost in the mapping process
        """
        
        enhanced_user_prompt = f"""
        TASK: Convert the following COMPLETE markdown-formatted resume content into the specified JSON schema.
        
        INPUT TYPE: Markdown format string containing ALL extracted resume information
        
        CRITICAL REQUIREMENTS:
        1. Process EVERY line of the markdown content below
        2. Extract ALL information - do not summarize, skip, or omit anything
        3. Map all content to appropriate JSON fields according to the schema
        4. Preserve all original details, descriptions, and specifics
        5. Include ALL experiences, education, skills, projects, achievements, etc.
        6. Maintain all dates, locations, company names, and technical terms exactly as provided
        7. Translate all text to English if users put other language in the resume
        
        MARKDOWN CONTENT TO PROCESS:
        ==========================================
        {pdf_contents}
        ==========================================
        
        Convert ALL the above markdown content into the complete JSON schema format. Ensure every piece of information is captured and properly structured.
        """
        
        response = groq_client.chat.completions.create(
            model=self.groq_model,
            messages=[
                {"role": "system", "content": enhanced_system_prompt},
                {"role": "user", "content": enhanced_user_prompt}
            ],
            stream=False,
            seed=23,
            temperature=0.5,  # Lower temperature for more consistent extraction
            top_p=1,
            presence_penalty=0,
            response_format={"type": "json_object"}
        )
        try:
            # Parse the response content as JSON first
            content_json = json.loads(response.choices[0].message.content)

            # Validate against schema
            try:
                adjustment_content = ResumeSchema.model_validate(content_json)
                # Use the custom encoder to handle any potential mappingproxy objects
                result_json = json.loads(json.dumps(adjustment_content.model_dump(), cls=ResumeJSONEncoder))
            except Exception as e:
                logger.error(f"Schema validation error: {str(e)}")
                # Fallback to direct use if validation fails
                result_json = content_json
            # Format all summary fields with HTML formatting using Groq
            try:
                # Collect all summary fields that need formatting
                summary_items = []
                
                def collect_summary_fields(data, path=""):
                    """Recursively collect all summary fields that need formatting"""
                    if isinstance(data, dict):
                        for key, value in data.items():
                            current_path = f"{path}.{key}" if path else key
                            if key == "summary" and isinstance(value, str) and len(value) > 10 and 'nextcareer.ai' not in value:
                                summary_items.append({
                                    "path": current_path,
                                    "content": value,
                                    "reference": data  # Keep reference to update later
                                })
                            elif isinstance(value, (dict, list)):
                                collect_summary_fields(value, current_path)
                    elif isinstance(data, list):
                        for i, item in enumerate(data):
                            current_path = f"{path}[{i}]" if path else f"[{i}]"
                            collect_summary_fields(item, current_path)

                # Special case for main summary section content
                main_summary_content = None
                if "sections" in result_json and "summary" in result_json["sections"] and "content" in result_json["sections"]["summary"]:
                    content = result_json["sections"]["summary"]["content"]
                    if isinstance(content, str) and len(content) > 10 and 'nextcareer.ai' not in content:
                        main_summary_content = content

                # Collect all summary fields
                collect_summary_fields(result_json)
                
                # If we have summaries to format, do it in one API call
                if summary_items or main_summary_content:
                    logger.info(f"Found {len(summary_items)} summary fields to format" + (", plus main summary" if main_summary_content else ""))
                    
                    # Prepare batch formatting prompt
                    all_summaries = []
                    if main_summary_content:
                        all_summaries.append(f"MAIN_SUMMARY:\n{main_summary_content}")
                    
                    for i, item in enumerate(summary_items):
                        all_summaries.append(f"SUMMARY_{i+1}:\n{item['content']}")
                    
                    batch_content = "\n\n---SEPARATOR---\n\n".join(all_summaries)
                    
                    # Single API call to format all summaries
                    LLM_FORMAT_MODEL = llm_helpers.get_available_groq_model()
                    logger.info(f"LLM_FORMAT_MODEL: {LLM_FORMAT_MODEL}")
                    
                    format_response = groq_client.chat.completions.create(
                        model=LLM_FORMAT_MODEL,
                        messages=[
                            {
                                "role": "system", 
                                "content": """You are an expert at formatting plain text into well-structured HTML. 
                                
I will provide multiple text sections separated by '---SEPARATOR---'. Each section starts with a label (MAIN_SUMMARY: or SUMMARY_N:).

Format each section into professional HTML with appropriate tags like <p>, <ul>, <li>, <strong>, <em>, etc. 
- Create logical paragraph breaks
- Convert bullet points to HTML lists  
- Emphasize important information
- Make content readable and professional
- Never use headings (<h1>, <h2>, ...)
- Do not edit, change or modify the content - just format it

Return the formatted sections in the same order, separated by '---SEPARATOR---', keeping the same labels."""
                            },
                            {
                                "role": "user", 
                                "content": f"Format these resume sections into professional HTML:\n\n{batch_content}"
                            }
                        ],
                        temperature=0.2,
                        top_p=1,
                        seed=4,
                        stream=False
                    )
                    
                    # Parse the formatted response
                    formatted_response = format_response.choices[0].message.content.strip()
                    
                    # Clean up any markdown code blocks
                    if formatted_response.startswith("```html"):
                        formatted_response = formatted_response.replace("```html", "").replace("```", "").strip()
                    
                    # Split back into individual formatted summaries
                    formatted_sections = formatted_response.split("---SEPARATOR---")
                    
                    # Apply formatted content back to the JSON
                    section_index = 0
                    
                    # Handle main summary first if it exists
                    if main_summary_content and section_index < len(formatted_sections):
                        formatted_main = formatted_sections[section_index].strip()
                        # Remove the label if present
                        if formatted_main.startswith("MAIN_SUMMARY:"):
                            formatted_main = formatted_main.replace("MAIN_SUMMARY:", "").strip()
                        result_json["sections"]["summary"]["content"] = formatted_main
                        logger.info("Formatted main summary content")
                        section_index += 1
                    
                    # Apply formatted content to other summary fields
                    for i, item in enumerate(summary_items):
                        if section_index < len(formatted_sections):
                            formatted_summary = formatted_sections[section_index].strip()
                            # Remove the label if present
                            label_prefix = f"SUMMARY_{i+1}:"
                            if formatted_summary.startswith(label_prefix):
                                formatted_summary = formatted_summary.replace(label_prefix, "").strip()
                            
                            # Safety check before updating reference
                            if isinstance(item, dict) and 'reference' in item and isinstance(item['reference'], dict):
                                item['reference']['summary'] = formatted_summary
                                logger.info(f"Formatted summary field {i+1}: {item.get('path', 'unknown')}")
                            else:
                                logger.warning(f"Invalid item structure for summary field {i+1}, skipping update")
                            section_index += 1

                    logger.info("Successfully formatted all summary fields with HTML in batch")
                else:
                    logger.info("No summary fields found to format")
                    
            except Exception as format_error:
                logger.error(f"Error formatting summaries with HTML: {str(format_error)}")
                # Continue with unformatted summaries if formatting fails
                raise ValueError(format_error)
            
            # Apply the check to ensure all required fields exist
            self.ensure_required_fields(result_json, self.required_fields)
            result_json["metadata"] = self.required_fields["metadata"]
            
            logger.info("Resume parsing completed successfully")
            return result_json
            
        except Exception as e:
            logger.error(f"Error processing resume JSON: {str(e)}")
            logger.error(f"Response content: {response.choices[0].message.content}")
            raise ValueError(f"Error serializing to JSON: {str(e)}")

    def get_pdf_pages_image(self, pdf_doc):
        # Open PDF from file path
        # pdf_doc = fitz.open(pdf_path)

        # Get dimensions from first page to set the combined image size
        first_page = pdf_doc[0]
        first_pix = first_page.get_pixmap()
        width = first_pix.width
        height = first_pix.height * len(pdf_doc)

        # Create a new image to hold all pages
        combined_image = Image.new("RGB", (width, height))

        # Process each page and add to the combined image
        for i in range(len(pdf_doc)):
            page = pdf_doc[i]
            pix = page.get_pixmap()
            img_data = pix.tobytes("png")
            page_image = Image.open(io.BytesIO(img_data))

            # Paste this page into the combined image
            combined_image.paste(page_image, (0, i * first_pix.height))

        return combined_image

    def parse_resume_from_pdf(self, pdf_bytes):
        """
        Parse a resume from a PDF file and return structured JSON data.

        Args:
            pdf_bytes (bytes): PDF file content as bytes

        Returns:
            dict: Structured resume data in JSON format
        """
        logger.info("Starting resume parsing from PDF bytes")

        # Get image from PDF
        logger.info("Converting PDF to image")
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        front_image = self.get_pdf_pages_image(pdf_doc)

        # Create a temporary file path
        logger.info("Creating temporary image file")
        temp_dir = os.path.dirname(os.path.abspath(__file__))
        temp_image_path = os.path.join(temp_dir, f"temp_resume_{uuid7()}.png")
        front_image.save(temp_image_path)
        logger.info(f"Saved temporary image to: {temp_image_path}")

        # Read the image file and convert to base64
        logger.info("Converting image to base64")
        with open(temp_image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        # Step 1: Extract text content from image using vision model
        logger.info("Step 1: Extracting text content from image using vision model")
        groq_client = Groq(api_key=self.groq_api_key)
        
        text_extraction_prompt = """
        You are an expert at reading and extracting text from resume images. 
        Please analyze the resume image and extract ALL text content in a well-structured markdown format.
        
        Include all sections such as:
        - Personal information (name, contact details, etc.)
        - Professional summary or objective
        - Work experience with job titles, companies, dates, and descriptions
        - Education with degrees, institutions, dates
        - Skills (technical and soft skills)
        - Projects with descriptions
        - Certifications
        - Awards and achievements
        - Languages
        - Volunteer work
        - Publications
        - References
        - Any other sections present
        
        Format the output as clean, well-structured markdown with appropriate headers, bullet points, and formatting.
        Preserve all the information exactly as it appears in the image.
        Do not summarize or omit any details - extract everything you can see.
        """
        
        text_response = groq_client.chat.completions.create(
            model="meta-llama/llama-4-scout-17b-16e-instruct",
            messages=[
                {
                    "role": "system",
                    "content": text_extraction_prompt
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "Please extract all text content from this resume image and format it as markdown."},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                    ]
                }
            ],
            temperature=0.5,
            top_p=1,
            stream=False
        )
        
        # Extract the text content
        pdf_contents = text_response.choices[0].message.content
        logger.info("Successfully extracted text content from image")
        logger.info(f"Extracted content length: {len(pdf_contents)} characters")
        logger.info(f"Extracted content: {pdf_contents}")
        logger.info("-"*20)

        # Clean up temporary file
        try:
            logger.info(f"Cleaning up temporary image file: {temp_image_path}")
            os.remove(temp_image_path)
        except Exception as e:
            logger.warning(f"Failed to remove temporary file: {str(e)}")

        # Step 2: Pass extracted text to query_resume_langchain
        logger.info("Step 2: Processing extracted text with query_resume_langchain")
        try:
            result_json = self.query_resume_langchain(pdf_contents)
            logger.info("Resume parsing completed successfully")
            return result_json
        except Exception as e:
            logger.error(f"Error processing resume with query_resume_langchain: {str(e)}")
            raise ValueError(f"Error processing resume: {str(e)}")
    
    def _safe_json_serialize(self, obj):
        """Safely serialize an object to JSON, handling special Python types."""
        if isinstance(obj, type):
            # If it's a class, return its name
            return obj.__name__
        elif hasattr(obj, "__dict__"):
            # For objects with __dict__, convert to dict
            return {k: self._safe_json_serialize(v) for k, v in obj.__dict__.items()
                   if not k.startswith("_")}
        elif isinstance(obj, (list, tuple)):
            # For lists and tuples
            return [self._safe_json_serialize(item) for item in obj]
        elif isinstance(obj, dict):
            # For dictionaries
            return {k: self._safe_json_serialize(v) for k, v in obj.items()}
        elif isinstance(obj, (int, float, str, bool, type(None))):
            # Basic types can be serialized directly
            return obj
        else:
            # For other types, convert to string
            return str(obj)

    def parse_resume_llama_4(self, pdf_bytes):
        """
        Parse a resume from PDF using vision model directly to JSON schema.
        Similar to query_resume_langchain but takes PDF input and uses vision model.

        Args:
            pdf_bytes (bytes): PDF file content as bytes

        Returns:
            dict: Structured resume data in JSON format
        """
        logger.info("Starting resume parsing with parse_resume_llama_4")

        # Get image from PDF
        logger.info("Converting PDF to image")
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        front_image = self.get_pdf_pages_image(pdf_doc)

        # Create a temporary file path
        logger.info("Creating temporary image file")
        temp_dir = os.path.dirname(os.path.abspath(__file__))
        temp_image_path = os.path.join(temp_dir, f"temp_resume_{uuid7()}.png")
        front_image.save(temp_image_path)
        logger.info(f"Saved temporary image to: {temp_image_path}")

        # Read the image file and convert to base64
        logger.info("Converting image to base64")
        with open(temp_image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        # Clean up temporary file
        try:
            logger.info(f"Cleaning up temporary image file: {temp_image_path}")
            os.remove(temp_image_path)
        except Exception as e:
            logger.warning(f"Failed to remove temporary file: {str(e)}")

        # Direct JSON schema generation using vision model
        groq_client: Groq = Groq(api_key=self.groq_api_key)
        
        # Enhanced system prompt for comprehensive extraction with strict JSON requirements
        enhanced_system_prompt = f"""
        {self.new_prompt_basic_info}

        CRITICAL JSON FORMATTING RULES - FOLLOW EXACTLY:
        
        1. OUTPUT ONLY VALID JSON - NO explanations, comments, or markdown
        2. ALL property names MUST be in double quotes: "propertyName"
        3. ALL string values MUST be in double quotes: "stringValue"
        4. USE COMMAS after every property EXCEPT the last one in each object
        5. USE COMMAS after every array item EXCEPT the last one
        6. NO trailing commas before closing brackets ] or braces }}
        7. NO single quotes - ONLY double quotes
        8. PROPERLY escape quotes inside strings with backslash: \"
        9. ENSURE all brackets and braces are properly matched and closed
        10. CHECK syntax before responding

        RESUME EXTRACTION REQUIREMENTS:
        - Extract ALL visible information from the resume image
        - Map content to the correct JSON schema fields exactly
        - Preserve original wording and technical terms
        - Include ALL experiences, education, skills, projects, achievements
        - Capture ALL dates, locations, company names exactly as shown
        - For any field that is not mentioned in the resume, leave it as empty string
        - DO NOT omit any information
        """
        
        enhanced_user_prompt = """
        TASK: Analyze this resume image and convert ALL content into valid JSON following the exact schema.
        - Translate all text to English

        CRITICAL JSON SYNTAX REQUIREMENTS:
        - Output MUST be valid JSON that can be parsed without errors
        - Every property name in double quotes
        - Every string value in double quotes
        - Correct comma placement (after each property/item except the last)
        - No trailing commas
        - No comments or extra text

        BEFORE RESPONDING:
        1. Double-check all comma placement
        2. Verify all quotes are properly closed
        3. Ensure all brackets/braces are matched
        4. Make sure it's valid JSON syntax

        Extract ALL information from the resume image and format as valid JSON now:
        """
        
        response = groq_client.chat.completions.create(
            model="meta-llama/llama-4-scout-17b-16e-instruct",
            messages=[
                {"role": "system", "content": enhanced_system_prompt},
                {
                    "role": "user", 
                    "content": [
                        {"type": "text", "text": enhanced_user_prompt},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                    ]
                }
            ],
            stream=False,
            seed=23,
            temperature=0.1,  # Very low temperature for consistent JSON syntax
            top_p=0.9,
            presence_penalty=0,
            response_format={"type": "json_object"}
        )
        
        try:
            # Parse the response content as JSON first
            content_json = json.loads(response.choices[0].message.content)

            # Validate against schema
            try:
                adjustment_content = ResumeSchema.model_validate(content_json)
                # Use the custom encoder to handle any potential mappingproxy objects
                result_json = json.loads(json.dumps(adjustment_content.model_dump(), cls=ResumeJSONEncoder))
            except Exception as e:
                logger.error(f"Schema validation error: {str(e)}")
                # Fallback to direct use if validation fails
                result_json = content_json
            
            # Format all summary fields with HTML formatting using Groq
            try:
                # Collect all summary fields that need formatting
                summary_items = []
                
                def collect_summary_fields(data, path=""):
                    """Recursively collect all summary fields that need formatting"""
                    if isinstance(data, dict):
                        for key, value in data.items():
                            current_path = f"{path}.{key}" if path else key
                            if key == "summary" and isinstance(value, str) and len(value) > 10 and 'nextcareer.ai' not in value:
                                summary_items.append({
                                    "path": current_path,
                                    "content": value,
                                    "reference": data  # Keep reference to update later
                                })
                            elif isinstance(value, (dict, list)):
                                collect_summary_fields(value, current_path)
                    elif isinstance(data, list):
                        for i, item in enumerate(data):
                            current_path = f"{path}[{i}]" if path else f"[{i}]"
                            collect_summary_fields(item, current_path)

                # Special case for main summary section content
                main_summary_content = None
                if "sections" in result_json and "summary" in result_json["sections"] and "content" in result_json["sections"]["summary"]:
                    content = result_json["sections"]["summary"]["content"]
                    if isinstance(content, str) and len(content) > 10 and 'nextcareer.ai' not in content:
                        main_summary_content = content

                # Collect all summary fields
                collect_summary_fields(result_json)
                
                # If we have summaries to format, do it in one API call
                if summary_items or main_summary_content:
                    logger.info(f"Found {len(summary_items)} summary fields to format" + (", plus main summary" if main_summary_content else ""))
                    
                    # Prepare batch formatting prompt
                    all_summaries = []
                    if main_summary_content:
                        all_summaries.append(f"MAIN_SUMMARY:\n{main_summary_content}")
                    
                    for i, item in enumerate(summary_items):
                        all_summaries.append(f"SUMMARY_{i+1}:\n{item['content']}")
                    
                    batch_content = "\n\n---SEPARATOR---\n\n".join(all_summaries)
                    
                    # Single API call to format all summaries
                    LLM_FORMAT_MODEL = llm_helpers.get_available_groq_model()
                    logger.info(f"LLM_FORMAT_MODEL: {LLM_FORMAT_MODEL}")
                    
                    format_response = groq_client.chat.completions.create(
                        model=LLM_FORMAT_MODEL,
                        messages=[
                            {
                                "role": "system", 
                                "content": """You are an expert at formatting plain text into well-structured HTML. 
                                
I will provide multiple text sections separated by '---SEPARATOR---'. 

Format each section into professional HTML with appropriate tags like <p>, <ul>, <li>, <strong>, <em>, etc. 
- Create logical paragraph breaks
- Convert bullet points to HTML lists  
- Emphasize important information
- Make content readable and professional
- Never use headings (<h1>, <h2>, ...)
- Do not edit, change or modify the content - just format it

Return the formatted sections in the same order, separated by '---SEPARATOR---', keeping the same labels."""
                            },
                            {
                                "role": "user", 
                                "content": f"Format these resume sections into professional HTML:\n\n{batch_content}"
                            }
                        ],
                        temperature=0.2,
                        top_p=1,
                        seed=4,
                        stream=False
                    )
                    
                    # Parse the formatted response
                    formatted_response = format_response.choices[0].message.content.strip()
                    
                    # Clean up any markdown code blocks
                    if formatted_response.startswith("```html"):
                        formatted_response = formatted_response.replace("```html", "").replace("```", "").strip()
                    
                    # Split back into individual formatted summaries
                    formatted_sections = formatted_response.split("---SEPARATOR---")
                    
                    # Apply formatted content back to the JSON
                    section_index = 0
                    
                    # Handle main summary first if it exists
                    if main_summary_content and section_index < len(formatted_sections):
                        formatted_main = formatted_sections[section_index].strip()
                        # Remove the label if present
                        if formatted_main.startswith("MAIN_SUMMARY:"):
                            formatted_main = formatted_main.replace("MAIN_SUMMARY:", "").strip()
                        result_json["sections"]["summary"]["content"] = formatted_main
                        logger.info("Formatted main summary content")
                        section_index += 1
                    
                    # Apply formatted content to other summary fields
                    for i, item in enumerate(summary_items):
                        if section_index < len(formatted_sections):
                            formatted_summary = formatted_sections[section_index].strip()
                            # Remove the label if present
                            label_prefix = f"SUMMARY_{i+1}:"
                            if formatted_summary.startswith(label_prefix):
                                formatted_summary = formatted_summary.replace(label_prefix, "").strip()
                            
                            # Safety check before updating reference
                            if isinstance(item, dict) and 'reference' in item and isinstance(item['reference'], dict):
                                item['reference']['summary'] = formatted_summary
                                logger.info(f"Formatted summary field {i+1}: {item.get('path', 'unknown')}")
                            else:
                                logger.warning(f"Invalid item structure for summary field {i+1}, skipping update")
                            section_index += 1

                    logger.info("Successfully formatted all summary fields with HTML in batch")
                else:
                    logger.info("No summary fields found to format")
                    
            except Exception as format_error:
                logger.error(f"Error formatting summaries with HTML: {str(format_error)}")
                # Continue with unformatted summaries if formatting fails
                raise ValueError(format_error)
            
            # Apply the check to ensure all required fields exist
            self.ensure_required_fields(result_json, self.required_fields)
            result_json["metadata"] = self.required_fields["metadata"]
            
            logger.info("Resume parsing with parse_resume_llama_4 completed successfully")
            return result_json
            
        except Exception as e:
            logger.error(f"Error processing resume JSON: {str(e)}")
            logger.error(f"Response content: {response.choices[0].message.content}")
            raise ValueError(f"Error serializing to JSON: {str(e)}")
