meta {
  name: ai/follow-ups/generate
  type: http
  seq: 1
}

post {
  url: {{LOCAL_API_HOST}}/ai/follow-ups/generate
  body: json
  auth: inherit
}

headers {
  Content-Type: application/json
  Authorization: Bearer eyJhbGciOiJQUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************.dY16hs-hWjj70MLDf1Z9Wu7-FPVenqogH9ZfrsgWqvfDZgGho4K0V5_HoE9yY02NRTT5f-IcXuJpqvXcNMgjxLsHm6zWVnfGLCoG8JPiMUYtreDCSkqA8p0FDLbBJV7fPVk81aA-nJiKhKdaFe5miTKeHfnnuU6H-nIgnFk8ng9ObcNi6ahfr8TcIfTMtEEvAh2LsE0k_DENBGpOxdWJdseBYBALtTDQk93O12rAIZtNf8_uCK_xcG1iimgNJ9DZwPdOLtSTwtaICpOTGk_vsaBCj9ByDc2Vm708pmW2ovyYGHS8vA4gMgLequkKSCouTB44nUiK-E-c-DrfyvMODA
}

body:json {
  {
    "resume_id": "1929b682-b198-40f1-8869-8560dc7f0ef5",
    "job_description_id": "06841503-e176-7ddc-8000-50660184b047",
    "force_regenerate": true
  }
}
