from enum import StrEnum
from polar_sdk import Polar, Checkout, CheckoutProductsCreate
from polar_sdk.models import SubscriptionCancel
from settings import POLAR_OAT, POLAR_SERVER


class CancellationReason(StrEnum):
    """Enum for subscription cancellation reasons."""
    TOO_EXPENSIVE = "too_expensive"
    MISSING_FEATURES = "missing_features"
    SWITCHED_SERVICE = "switched_service"
    UNUSED = "unused"
    CUSTOMER_SERVICE = "customer_service"
    LOW_QUALITY = "low_quality"
    TOO_COMPLEX = "too_complex"
    OTHER = "other"


def checkout(product_id: str, user: dict, redirect_url: str) -> Checkout:
    if not isinstance(product_id, str):
        product_id = str(product_id)
    if POLAR_SERVER != "sandbox":
        with Polar(access_token=POLAR_OAT) as polar:
            checkout = polar.checkouts.create(request=CheckoutProductsCreate(**{
                "allow_discount_codes": True,
                "products": [product_id,],
                "success_url": redirect_url,
                "customer_name": user['fullname'],
                "customer_email": user['email'],
                "customer_external_id": user['id'],
            }))
            return checkout

    with Polar(server=POLAR_SERVER, access_token=POLAR_OAT) as polar:
        checkout = polar.checkouts.create(request=CheckoutProductsCreate(**{
            "allow_discount_codes": True,
            "products": [product_id,],
            "success_url": redirect_url,
            "customer_name": user['fullname'],
            "customer_email": user['email'],
            "customer_external_id": user['id'],
        }))
        return checkout


def cancel_subscription(
    subscription_id: str,
    cancel_at_period_end: bool = True,
    customer_cancellation_reason: CancellationReason = CancellationReason.CUSTOMER_SERVICE,
    customer_cancellation_comment: str = "Customer requested cancellation"
):
    """Cancel a Polar subscription using the Polar SDK.

    Args:
        subscription_id (str): The Polar subscription ID to cancel
        cancel_at_period_end (bool): Whether to cancel at the end of billing period. Defaults to True.
        customer_cancellation_reason (CancellationReason): Reason for cancellation. Defaults to CancellationReason.CUSTOMER_SERVICE.
        customer_cancellation_comment (str): Comment for cancellation. Defaults to "Customer requested cancellation".

    Returns:
        The updated subscription object from Polar API

    Raises:
        ValueError: If subscription_id is invalid
        Exception: If the API request fails
    """
    if not subscription_id:
        raise ValueError("subscription_id is required")

    if not isinstance(subscription_id, str):
        subscription_id = str(subscription_id)

    # Create the cancellation update data
    update_data = SubscriptionCancel(
        cancel_at_period_end=cancel_at_period_end,
        customer_cancellation_reason=customer_cancellation_reason,
        customer_cancellation_comment=customer_cancellation_comment
    )

    if POLAR_SERVER != "sandbox":
        with Polar(access_token=POLAR_OAT) as polar:
            return polar.subscriptions.update(
                id=subscription_id,
                subscription_update=update_data
            )

    with Polar(server=POLAR_SERVER, access_token=POLAR_OAT) as polar:
        return polar.subscriptions.update(
            id=subscription_id,
            subscription_update=update_data
        )
