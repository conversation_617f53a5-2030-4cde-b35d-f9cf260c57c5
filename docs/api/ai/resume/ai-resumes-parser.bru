meta {
  name: ai/resumes/parser
  type: http
  seq: 3
}

post {
  url: {{LOCAL_API_HOST}}/ai/resumes/parser
  body: multipartForm
  auth: inherit
}

headers {
  Authorization: Bearer eyJhbGciOiJQUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************.gkfCU0mDqnMOagpkPf8SzefhdY3c8HWQxr_FGBbOIdNPi21veOAbOFA0FsZJFMv-nTOZUmTlvA1Mkf3uqqPSjQymPkweg9vfywVR1DroHRvo7q7mwVR3_BdCDtuEdTxsHspQX-tjw4MCKFycSe7VPpvwWI2HnPOzUC82xeHTentmmeXzVdNzdv17Pq8fY6cYGGkHKsBkkZ1NS73x4vXGhaceyenIP_jE887k6gmCEzJwGO4tErFWe3NCkIh9L7TTWxohV9naV-XZc-bBjgC4jMPGLauO2vvsXB5_WA7QRQS5ukm0arFVHR34oQ__RfTlZgt2GGhYZvSZQ5hvx41mbA
}

body:multipart-form {
  resume_file: @file(/Users/<USER>/Downloads/TieuKimHaoCV.pdf)
}
