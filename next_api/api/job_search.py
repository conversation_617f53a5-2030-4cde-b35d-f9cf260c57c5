from sanic import Blueprint, Request
from sanic_ext import openapi
from typing import List
import asyncio
from datetime import datetime
import os

from models.job_search import JobSearchRequest, JobResponse
from clients.job_search.adzuna import <PERSON>zunaProvider
from clients.job_search.findwork import FindworkProvider
from clients.job_search.careerjet import CareerjetProvider
from clients.job_search.jooble import JoobleProvider
from clients.job_search.base import JobSearchProvider
from api.base import json_response, Response, ResponseCode
from sanic.log import logger

bp = Blueprint("job_search")

# Initialize providers
providers: List[JobSearchProvider] = [
    AdzunaProvider(
        api_key=os.getenv("ADZUNA_API_KEY"),
        app_id=os.getenv("ADZUNA_APP_ID")
    ),
    FindworkProvider(
        api_key=os.getenv("FINDWORK_API_KEY")
    ),
    CareerjetProvider(
        api_key=os.getenv("CAREERJET_API_KEY")
    ),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
        api_key=os.getenv("JOOBLE_API_KEY")
    )
]

def sort_jobs(jobs: List[JobResponse], request: JobSearchRequest) -> List[JobResponse]:
    """
    Sort job results based on relevance to the search request.
    Prioritizes matches in title and description over location matches.
    Filters out jobs that don't have meaningful matches with the search keyword.
    """
    # Create a scoring function for each job
    def score_job(job: JobResponse) -> float:
        score = 0.0
        keyword = request.keyword.lower()
        has_keyword_match = False  # Flag to track if job has any keyword match
        
        # Score based on keyword matches in title
        title_lower = job.title.lower()
        if keyword in title_lower:
            has_keyword_match = True
            # Base score for title match
            score += 20.0
            
            # Bonus for exact title match
            if title_lower == keyword:
                score += 10.0
            
            # Bonus for keyword frequency in title
            keyword_count = title_lower.count(keyword)
            score += min(keyword_count * 5.0, 15.0)  # Cap at 15 points for multiple occurrences
        
        # Score based on keyword matches in description
        if job.description:
            desc_lower = job.description.lower()
            if keyword in desc_lower:
                has_keyword_match = True
                # Base score for description match
                score += 10.0
                
                # Bonus for keyword frequency in description
                keyword_count = desc_lower.count(keyword)
                score += min(keyword_count * 2.0, 10.0)  # Cap at 10 points for multiple occurrences
        
        # Only add location score if there's a keyword match
        if has_keyword_match and request.location and job.location:
            if request.location.lower() in job.location.lower():
                score += 5.0
        
        # Only add recency score if there's a keyword match
        if has_keyword_match and job.posted_at:
            days_old = (datetime.now().date() - job.posted_at).days
            if days_old < 7:  # Less than a week old
                score += 3.0
            elif days_old < 30:  # Less than a month old
                score += 2.0
            elif days_old < 90:  # Less than 3 months old
                score += 1.0
        
        # Only add remote and salary scores if there's a keyword match
        if has_keyword_match:
            if job.is_remote:
                score += 1.0
            if job.salary:
                score += 0.5
        
        # Return a tuple of (score, has_keyword_match) for filtering
        return (score, has_keyword_match)
    
    # Score all jobs
    scored_jobs = [(job, score_job(job)) for job in jobs]
    
    # Filter out jobs without keyword matches and sort by score
    filtered_jobs = [
        job for job, (score, has_match) in scored_jobs 
        if has_match and score >= 10.0  # Minimum score threshold
    ]
    
    # Sort by score (we only need the score part of the tuple now)
    return sorted(filtered_jobs, key=lambda job: score_job(job)[0], reverse=True)


@openapi.tag("Job Search")
@bp.route("/search", methods=["POST"], strict_slashes=False)
async def search_jobs(request: Request):
    """
    Search for jobs across all providers

    openapi:
    ---
    summary: Search for jobs across multiple job boards
    description: This endpoint searches for jobs across multiple job boards and returns aggregated, sorted results.
    requestBody:
      content:
        application/json:
          schema:
            ref: "#/components/schemas/JobSearchRequest"
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: array
              items:
                ref: "#/components/schemas/JobResponse"
      400:
        description: Bad Request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/ErrorResponse"
      500:
        description: Server Error
        content:
          application/json:
            schema:
              ref: "#/components/schemas/ErrorResponse"
    """
    try:
        # Parse request body
        search_request = JobSearchRequest(**request.json)
        
        # Search jobs concurrently across all providers
        tasks = []
        for provider in providers:
            try:
                task = provider.search_jobs(search_request)
                tasks.append(task)
            except Exception as e:
                logger.error(f"Error creating task for provider {provider.__class__.__name__}: {e}")
                continue
        
        # Gather results, handling individual provider failures
        results = []
        provider_results = {}
        completed_tasks = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, task_result in enumerate(completed_tasks):
            provider_name = providers[i].__class__.__name__ if i < len(providers) else f"Provider_{i}"
            
            if isinstance(task_result, Exception):
                logger.error(f"Provider {provider_name} error: {task_result}")
                provider_results[provider_name] = []
                continue
                
            if isinstance(task_result, list):
                provider_results[provider_name] = task_result
                results.extend(task_result)
            else:
                logger.error(f"Unexpected result type from {provider_name}: {type(task_result)}")
                provider_results[provider_name] = []
        
        # Log summary of results from each provider
        logger.info("Search results summary:")
        provider_jobs_count = {}
        for provider_name, provider_jobs in provider_results.items():
            provider_jobs_count[provider_name] = len(provider_jobs)
            logger.info(f"  {provider_name}: {len(provider_jobs)} jobs")
        
        # Sort the combined results
        sorted_results = sort_jobs(results, search_request)
        
        return json_response(Response(
            error=ResponseCode.SUCCESS,
            message="Success",
            data={"providers_count": provider_jobs_count, 
                  "jobs_found": sorted_results}
        ), status_code=200)
        
    except Exception as e:
        logger.error(f"Error in search_jobs: {e}")
        return json_response(Response(
            error=ResponseCode.SERVER_ERROR,
            message=str(e),
            data={}
        ), status_code=500) 