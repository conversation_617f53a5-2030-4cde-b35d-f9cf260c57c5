from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, ConfigDict, field_validator
from tortoise.expressions import RawSQL
from models.job_description import JobDescription
from repositories.base import Pagination, OrderBy


class JobDescriptionOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "position", "-position",
        "company", "-company",
        "posted_at", "-posted_at",
        "status", "-status",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
    )
    _default_order_by = ("-posted_at", "-created_at")


class JobDescriptionQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    user_id: Optional[UUID] = None
    user_id__in: Optional[List[UUID]] = None
    user_id__not_in: Optional[List[UUID]] = None

    description: Optional[str] = None
    description__contains: Optional[str] = None
    description__icontains: Optional[str] = None

    position: Optional[str] = None
    position__contains: Optional[str] = None
    position__icontains: Optional[str] = None

    company: Optional[str] = None
    company__contains: Optional[str] = None
    company__icontains: Optional[str] = None

    city: Optional[str] = None
    city__contains: Optional[str] = None
    city__icontains: Optional[str] = None

    country: Optional[str] = None
    country__contains: Optional[str] = None
    country__icontains: Optional[str] = None

    posted_at: Optional[datetime] = None
    posted_at__gte: Optional[datetime] = None
    posted_at__lte: Optional[datetime] = None

    salary: Optional[float] = None
    salary__gte: Optional[float] = None
    salary__lte: Optional[float] = None

    link: Optional[str] = None
    link__contains: Optional[str] = None
    link__icontains: Optional[str] = None

    is_remote: Optional[bool] = None

    source: Optional[str] = None
    source__contains: Optional[str] = None
    source__icontains: Optional[str] = None

    category: Optional[str] = None
    category__contains: Optional[str] = None
    category__icontains: Optional[str] = None

    keywords__contains: Optional[List[str]] = None

    skills__contains: Optional[List[str]] = None

    status: Optional[JobDescription.Status] = None
    status__in: Optional[List[JobDescription.Status]] = None
    status__not_in: Optional[List[JobDescription.Status]] = None

    contract_type: Optional[JobDescription.ContractType] = None
    contract_type__in: Optional[List[JobDescription.ContractType]] = None
    contract_type__not_in: Optional[List[JobDescription.ContractType]] = None

    is_favorite: Optional[bool] = None

    promotion: Optional[JobDescription.PromotionType] = None
    promotion__in: Optional[List[JobDescription.PromotionType]] = None
    promotion__not_in: Optional[List[JobDescription.PromotionType]] = None

    mock_interview_id: Optional[UUID] = None
    resume_id: Optional[UUID] = None
    follow_up_id: Optional[UUID] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    @field_validator(
        "id", "user_id", "mock_interview_id", "resume_id", "follow_up_id",
        mode="before"
    )
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator(
        "id__in", "id__not_in",
        "user_id__in", "user_id__not_in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(',')]
        return v

    @field_validator(
        "description", "description__contains", "description__icontains",
        "position", "position__contains", "position__icontains",
        "company", "company__contains", "company__icontains",
        "city", "city__contains", "city__icontains",
        "country", "country__contains", "country__icontains",
        "link", "link__contains", "link__icontains",
        "source", "source__contains", "source__icontains",
        "category", "category__contains", "category__icontains",
        mode="before"
    )
    @classmethod
    def validate_string(cls, v: Any) -> Any:
        if isinstance(v, list):
            return v[0]
        return v

    @field_validator(
        "skills__contains", "keywords__contains",
        mode="before"
    )
    @classmethod
    def validate_str_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return v.split(',')
        if isinstance(v, list):
            result = []
            for item in v:
                result.extend(item.split(','))
            return result
        return v

    @field_validator(
        "posted_at", "posted_at__gte", "posted_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime_posted(cls, v: Any) -> Any:
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v

    @field_validator(
        "salary", "salary__gte", "salary__lte",
        mode="before"
    )
    @classmethod
    def validate_float(cls, v: Any) -> Any:
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator(
        "created_at", "created_at__gte", "created_at__lte",
        "updated_at", "updated_at__gte", "updated_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v


class JobDescriptionRepository:
    @staticmethod
    async def create_one(job_description: Dict[str, Any]) -> JobDescription:
        """Create a single job description record."""
        return await JobDescription.create(**job_description)

    @staticmethod
    async def create_many(job_descriptions: List[Dict[str, Any]]) -> List[JobDescription]:
        """Create multiple job description records in bulk."""
        return await JobDescription.bulk_create([JobDescription(**job) for job in job_descriptions])

    @staticmethod
    async def get_one(q: JobDescriptionQuery) -> Optional[JobDescription]:
        """Get a single job description that matches the ID in the query."""
        return await JobDescription.get_or_none(id=q.id)

    @staticmethod
    async def get_list(q: JobDescriptionQuery, p: Pagination, as_dict: bool = True)\
            -> Tuple[Union[List[dict], List[JobDescription]], int]:
        """Get a paginated list of job descriptions matching the query criteria."""
        query = JobDescription.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_id__not_in is not None:
            query = query.filter(user_id__not_in=q.user_id__not_in)
        if q.description is not None:
            query = query.filter(description=q.description)
        if q.description__contains is not None:
            query = query.filter(description__contains=q.description__contains)
        if q.description__icontains is not None:
            query = query.filter(description__icontains=q.description__icontains)
        if q.position is not None:
            query = query.filter(position=q.position)
        if q.position__contains is not None:
            query = query.filter(position__contains=q.position__contains)
        if q.position__icontains is not None:
            query = query.filter(position__icontains=q.position__icontains)
        if q.company is not None:
            query = query.filter(company=q.company)
        if q.company__contains is not None:
            query = query.filter(company__contains=q.company__contains)
        if q.company__icontains is not None:
            query = query.filter(company__icontains=q.company__icontains)
        if q.city is not None:
            query = query.filter(city=q.city)
        if q.city__contains is not None:
            query = query.filter(city__contains=q.city__contains)
        if q.city__icontains is not None:
            query = query.filter(city__icontains=q.city__icontains)
        if q.country is not None:
            query = query.filter(country=q.country)
        if q.country__contains is not None:
            query = query.filter(country__contains=q.country__contains)
        if q.country__icontains is not None:
            query = query.filter(country__icontains=q.country__icontains)
        if q.posted_at is not None:
            query = query.filter(posted_at=q.posted_at)
        if q.posted_at__gte is not None:
            query = query.filter(posted_at__gte=q.posted_at__gte)
        if q.posted_at__lte is not None:
            query = query.filter(posted_at__lte=q.posted_at__lte)
        if q.salary is not None:
            query = query.filter(salary=q.salary)
        if q.salary__gte is not None:
            query = query.filter(salary__gte=q.salary__gte)
        if q.salary__lte is not None:
            query = query.filter(salary__lte=q.salary__lte)
        if q.link is not None:
            query = query.filter(link=q.link)
        if q.link__contains is not None:
            query = query.filter(link__contains=q.link__contains)
        if q.link__icontains is not None:
            query = query.filter(link__icontains=q.link__icontains)
        if q.is_remote is not None:
            query = query.filter(is_remote=q.is_remote)
        if q.source is not None:
            query = query.filter(source=q.source)
        if q.source__contains is not None:
            query = query.filter(source__contains=q.source__contains)
        if q.source__icontains is not None:
            query = query.filter(source__icontains=q.source__icontains)
        if q.category is not None:
            query = query.filter(category=q.category)
        if q.category__contains is not None:
            query = query.filter(category__contains=q.category__contains)
        if q.category__icontains is not None:
            query = query.filter(category__icontains=q.category__icontains)
        if q.keywords__contains is not None:
            query = query.annotate(keywords_contains=RawSQL(
                "keywords @> ARRAY[" + ",".join([f"'{keyword}'" for keyword in q.keywords__contains]) + "]::TEXT[]"))
            query = query.filter(keywords_contains=True)
        if q.skills__contains is not None:
            query = query.annotate(skills_contains=RawSQL(
                "skills @> ARRAY[" + ",".join([f"'{skill}'" for skill in q.skills__contains]) + "]::TEXT[]"))
            query = query.filter(skills_contains=True)
        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)
        if q.contract_type is not None:
            query = query.filter(contract_type=q.contract_type)
        if q.contract_type__in is not None:
            query = query.filter(contract_type__in=q.contract_type__in)
        if q.contract_type__not_in is not None:
            query = query.filter(contract_type__not_in=q.contract_type__not_in)
        if q.is_favorite is not None:
            query = query.filter(is_favorite=q.is_favorite)
        if q.promotion is not None:
            query = query.filter(promotion=q.promotion)
        if q.promotion__in is not None:
            query = query.filter(promotion__in=q.promotion__in)
        if q.promotion__not_in is not None:
            query = query.filter(promotion__not_in=q.promotion__not_in)
        if q.mock_interview_id is not None:
            query = query.filter(mock_interview_id=q.mock_interview_id)
        if q.resume_id is not None:
            query = query.filter(resume_id=q.resume_id)
        if q.follow_up_id is not None:
            query = query.filter(follow_up_id=q.follow_up_id)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        total = await query.count()
        order_by = JobDescriptionOrderBy(q.order_by)
        if as_dict:
            jobs = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).all().values()
        else:
            jobs = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).all()

        return jobs, total

    @staticmethod
    async def update(pk: Union[str, UUID], data: Union[dict, JobDescription]) -> Tuple[int, Optional[JobDescription]]:
        """Update a job description record."""
        if isinstance(pk, str):
            pk = UUID(pk)
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()
        if isinstance(data, dict):
            affected_rows = await JobDescription.filter(id=pk).update(**data)
        else:
            affected_rows = await JobDescription.filter(id=pk).update(data)

        updated_job = None
        if affected_rows > 0:
            updated_job = await JobDescription.get(pk=pk)

        return affected_rows, updated_job