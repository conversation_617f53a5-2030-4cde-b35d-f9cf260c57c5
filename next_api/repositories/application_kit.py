from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, ConfigDict, field_validator
from models.application_kit import ApplicationKit
from repositories.base import Pagination, OrderBy


class ApplicationKitOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
        "status", "-status",
    )
    _default_order_by = ("created_at",)


class ApplicationKitQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    user_id: Optional[UUID] = None
    user_id__in: Optional[List[UUID]] = None
    user_id__not_in: Optional[List[UUID]] = None

    jd_id: Optional[UUID] = None
    jd_id__in: Optional[List[UUID]] = None
    jd_id__not_in: Optional[List[UUID]] = None

    resume_id: Optional[UUID] = None
    resume_id__in: Optional[List[UUID]] = None
    resume_id__not_in: Optional[List[UUID]] = None

    cover_letter_id: Optional[UUID] = None
    cover_letter_id__in: Optional[List[UUID]] = None
    cover_letter_id__not_in: Optional[List[UUID]] = None

    follow_up_id: Optional[UUID] = None
    follow_up_id__in: Optional[List[UUID]] = None
    follow_up_id__not_in: Optional[List[UUID]] = None

    mock_interview_id: Optional[UUID] = None
    mock_interview_id__in: Optional[List[UUID]] = None
    mock_interview_id__not_in: Optional[List[UUID]] = None

    job_insight_id: Optional[UUID] = None
    job_insight_id__in: Optional[List[UUID]] = None
    job_insight_id__not_in: Optional[List[UUID]] = None

    status: Optional[int] = None
    status__in: Optional[List[int]] = None
    status__not_in: Optional[List[int]] = None
    status__gte: Optional[int] = None
    status__lte: Optional[int] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    @field_validator(
        "id", "user_id", "jd_id", "resume_id", "cover_letter_id",
        "follow_up_id", "mock_interview_id", "job_insight_id",
        mode="before"
    )
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator(
        "id__in", "id__not_in",
        "user_id__in", "user_id__not_in",
        "jd_id__in", "jd_id__not_in",
        "resume_id__in", "resume_id__not_in",
        "cover_letter_id__in", "cover_letter_id__not_in",
        "follow_up_id__in", "follow_up_id__not_in",
        "mock_interview_id__in", "mock_interview_id__not_in",
        "job_insight_id__in", "job_insight_id__not_in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(',')]
        return v

    @field_validator(
        "created_at", "created_at__gte", "created_at__lte",
        "updated_at", "updated_at__gte", "updated_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        return datetime.fromisoformat(v)
    
    @field_validator(
        "status",
        mode="before"
    )
    @classmethod
    def validate_int(cls, v: Any) -> int:
        if isinstance(v, str):
            v = int(v)
        elif isinstance(v, list):
            v = int(v[0])
        return v


class ApplicationKitRepository:

    @staticmethod
    async def create_one(application_kit: Dict[str, Any]) -> dict:
        """Create a single application kit record."""
        record = await ApplicationKit.create(**application_kit)
        return dict(record)

    @staticmethod
    async def create_many(application_kits: List[Dict[str, Any]]) -> List[ApplicationKit]:
        """Create multiple application kit records in bulk."""
        return await ApplicationKit.bulk_create([ApplicationKit(**kit) for kit in application_kits])

    @staticmethod
    async def get_one(q: ApplicationKitQuery) -> Optional[dict]:
        """Get a single application kit record that matches the ID in the query."""
        return await ApplicationKit.get_or_none(id=q.id).values()

    @staticmethod
    async def get_list(q: ApplicationKitQuery, p: Pagination) -> Tuple[List[dict], int]:
        """Get a paginated list of application kits matching the query criteria."""
        query = ApplicationKit.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_id__not_in is not None:
            query = query.filter(user_id__not_in=q.user_id__not_in)
        if q.jd_id is not None:
            query = query.filter(jd_id=q.jd_id)
        if q.jd_id__in is not None:
            query = query.filter(jd_id__in=q.jd_id__in)
        if q.jd_id__not_in is not None:
            query = query.filter(jd_id__not_in=q.jd_id__not_in)
        if q.resume_id is not None:
            query = query.filter(resume_id=q.resume_id)
        if q.resume_id__in is not None:
            query = query.filter(resume_id__in=q.resume_id__in)
        if q.resume_id__not_in is not None:
            query = query.filter(resume_id__not_in=q.resume_id__not_in)
        if q.cover_letter_id is not None:
            query = query.filter(cover_letter_id=q.cover_letter_id)
        if q.cover_letter_id__in is not None:
            query = query.filter(cover_letter_id__in=q.cover_letter_id__in)
        if q.cover_letter_id__not_in is not None:
            query = query.filter(cover_letter_id__not_in=q.cover_letter_id__not_in)
        if q.follow_up_id is not None:
            query = query.filter(follow_up_id=q.follow_up_id)
        if q.follow_up_id__in is not None:
            query = query.filter(follow_up_id__in=q.follow_up_id__in)
        if q.follow_up_id__not_in is not None:
            query = query.filter(follow_up_id__not_in=q.follow_up_id__not_in)
        if q.mock_interview_id is not None:
            query = query.filter(mock_interview_id=q.mock_interview_id)
        if q.mock_interview_id__in is not None:
            query = query.filter(mock_interview_id__in=q.mock_interview_id__in)
        if q.mock_interview_id__not_in is not None:
            query = query.filter(mock_interview_id__not_in=q.mock_interview_id__not_in)
        if q.job_insight_id is not None:
            query = query.filter(job_insight_id=q.job_insight_id)
        if q.job_insight_id__in is not None:
            query = query.filter(job_insight_id__in=q.job_insight_id__in)
        if q.job_insight_id__not_in is not None:
            query = query.filter(job_insight_id__not_in=q.job_insight_id__not_in)
        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)
        if q.status__gte is not None:
            query = query.filter(status__gte=q.status__gte)
        if q.status__lte is not None:
            query = query.filter(status__lte=q.status__lte)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        total = await query.count()
        order_by = ApplicationKitOrderBy(q.order_by)
        kits = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).all().values()

        return kits, total

    @staticmethod
    async def update(pk: Union[str, UUID], data: Union[dict, ApplicationKit]) -> Tuple[int, Optional[dict]]:
        """Update an application kit record."""
        if isinstance(pk, str):
            pk = UUID(pk)
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()
        if isinstance(data, dict):
            affected_rows = await ApplicationKit.filter(id=pk).update(**data)
        else:
            affected_rows = await ApplicationKit.filter(id=pk).update(data)

        updated_kit = None
        if affected_rows > 0:
            updated_kit = await ApplicationKit.get(pk=pk).values()

        return affected_rows, updated_kit
