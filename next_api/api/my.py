from typing import Union
from sanic import Blueprint
from sanic.log import error_logger
from sanic_ext import openapi
from api.base import json_response, Response, ListResponse, SUCCESS_MESSAGE, ResponseCode, \
    OPENAPI_BAD_REQUEST_RESPONSE, OPENAPI_NOT_FOUND_RESPONSE, OPENAPI_UNAUTHORIZED_RESPONSE
from api.middlewares import require_token
from repositories.base import Pagination
from repositories.resume import ResumeRepository, ResumeQuery
from repositories.cover_letter import CoverLetterRepository, CoverLetterQuery
from repositories.user_saved_job import UserSavedJobRepository
from repositories.search_job import SearchJobRepository
from repositories.job_description import JobDescriptionRepository
from datetime import datetime

bp = Blueprint("my")


@bp.route("/resumes", methods=["GET"], strict_slashes=False)
@openapi.definition(
    summary="Get a list of resumes of current user",
    description="This endpoint is used to get resume list.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string",
            description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "page", int, location="query", description="Page number", required=False, default=1),
        openapi.definitions.Parameter(
            "page_size", int, location="query", description="Number of records per page",
            required=False, default=10),
        openapi.definitions.Parameter(
            "id", str, location="query", description="Resume ID", required=False),
        openapi.definitions.Parameter(
            "id__in", Union[list[str], str], location="query", description="Resume ID list", required=False),
        openapi.definitions.Parameter(
            "id__not_in", Union[list[str], str], location="query", description="Resume ID list", required=False),
        openapi.definitions.Parameter(
            "title", str, location="query", description="Title", required=False),
        openapi.definitions.Parameter(
            "title__contains", str, location="query", description="Title contains", required=False),
        openapi.definitions.Parameter(
            "title__icontains", str, location="query",
            description="Title contains, search case insensitive", required=False),
        openapi.definitions.Parameter(
            "title__startswith", str, location="query", description="Title starts with", required=False),
        openapi.definitions.Parameter(
            "title__endswith", str, location="query", description="Title ends with", required=False),
        openapi.definitions.Parameter(
            "content", str, location="query", description="Content", required=False),
        openapi.definitions.Parameter(
            "content__contains", str, location="query", description="Content contains", required=False),
        openapi.definitions.Parameter(
            "content__icontains", str, location="query",
            description="Content contains, search case insensitive", required=False),
        openapi.definitions.Parameter(
            "keywords", Union[list[str], str], location="query", description="Keywords", required=False),
        openapi.definitions.Parameter(
            "status", int, location="query", description="Status", required=False),
        openapi.definitions.Parameter(
            "status__in", Union[list[int], int], location="query", description="Status list", required=False),
        openapi.definitions.Parameter(
            "status__not_in", Union[list[int], int], location="query", description="Status list", required=False),
        openapi.definitions.Parameter(
            "created_at", str, location="query", description="Created at", required=False),
        openapi.definitions.Parameter(
            "created_at__gte", str, location="query",
            description="Created at greater than or equal to", required=False),
        openapi.definitions.Parameter(
            "created_at__lte", str, location="query",
            description="Created at less than or equal to", required=False),
        openapi.definitions.Parameter(
            "updated_at", str, location="query", description="Updated at", required=False),
        openapi.definitions.Parameter(
            "updated_at__gte", str, location="query",
            description="Updated at greater than or equal to", required=False),
        openapi.definitions.Parameter(
            "updated_at__lte", str, location="query",
            description="Updated at less than or equal to", required=False),
        openapi.definitions.Parameter(
            "order_by", str, location="query", description="Order by", required=False),
    ],
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully retrieved a resume list",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {
                                "type": "integer",
                                "description": "Error code, 0 means success"
                            },
                            "message": {
                                "type": "string",
                                "description": "Response message"
                            },
                            "data": {
                                "type": "object",
                                "properties": {
                                    "total": {"type": "integer", "description": "Total number of records"},
                                    "page": {"type": "integer", "description": "Current page number"},
                                    "page_size": {"type": "integer", "description": "Number of records per page"},
                                    "records": {
                                        "type": "array",
                                        "items": {
                                            "type": "object",
                                            "$ref": "#/components/schemas/ResponseResumeSchema"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_UNAUTHORIZED_RESPONSE,
        OPENAPI_BAD_REQUEST_RESPONSE,
    ]
)
@openapi.tag("Resume", "Current User")
@require_token
async def get_list_resumes(request):
    """Get a paginated list of resumes."""
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query_args = request.args
    query_args['user_id'] = request.ctx.user['id']
    query = ResumeQuery(**query_args)
    records, total = await ResumeRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@bp.route("/cover-letters", methods=["GET"], strict_slashes=False)
@openapi.definition(
    summary="Get a paginated list of cover letters of current user",
    description="This endpoint is used to get a paginated list of cover letters.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            name="page", location="query", type="integer", description="Page number, default is 1"),
        openapi.definitions.Parameter(
            name="page_size", location="query", type="integer",
            description="Number of records per page, default is 10"),
        openapi.definitions.Parameter(
            name="id", location="query", type="string", description="UUID of cover letter"),
        openapi.definitions.Parameter(
            name="id__in", location="query", type="array", description="List of UUIDs of cover letters"),
        openapi.definitions.Parameter(
            name="id__not_in", location="query", type="array",
            description="List of UUIDs of cover letters not to include"),
        openapi.definitions.Parameter(
            name="resume_id", location="query", type="string", description="UUID of resume"),
        openapi.definitions.Parameter(
            name="resume_id__in", location="query", type="array", description="List of UUIDs of resumes"),
        openapi.definitions.Parameter(
            name="resume_id__not_in", location="query",
            type="array", description="List of UUIDs of resumes not to include"),
        openapi.definitions.Parameter(
            name="title", location="query", type="string", description="Title of cover letter"),
        openapi.definitions.Parameter(
            name="title__contains", location="query", type="string", description="Title contains"),
        openapi.definitions.Parameter(
            name="title__icontains", location="query",
            type="string", description="Title contains, search case insensitive"),
        openapi.definitions.Parameter(
            name="title__startswith", location="query", type="string", description="Title starts with"),
        openapi.definitions.Parameter(
            name="title__endswith", location="query", type="string", description="Title ends with"),
        openapi.definitions.Parameter(
            name="content", location="query", type="string", description="Content of cover letter"),
        openapi.definitions.Parameter(
            name="content__contains", location="query", type="string", description="Content contains"),
        openapi.definitions.Parameter(
            name="content__icontains", location="query",
            type="string", description="Content contains, search case insensitive"),
        openapi.definitions.Parameter(
            "target_position", str, location="query", description="Target position", required=False),
        openapi.definitions.Parameter(
            "target_position__contains", str, location="query",
            description="Target position contains", required=False),
        openapi.definitions.Parameter(
            "target_position__icontains", str, location="query",
            description="Target position contains (case insensitive)", required=False),
        openapi.definitions.Parameter(
            "target_position__startswith", str, location="query",
            description="Target position starts with", required=False),
        openapi.definitions.Parameter(
            "target_position__endswith", str, location="query",
            description="Target position ends with", required=False),
        openapi.definitions.Parameter(
            name="status", location="query", type="integer", description="Status of cover letter"),
        openapi.definitions.Parameter(
            name="status__in", location="query", type="array", description="List of statuses of cover letters"),
        openapi.definitions.Parameter(
            name="status__not_in", location="query", type="array",
            description="List of statuses of cover letters not to include"),
        openapi.definitions.Parameter(
            name="created_at", location="query", type="string", description="Created at, format is ISO 8601"),
        openapi.definitions.Parameter(
            name="created_at__gte", location="query",
            type="string", description="Created at greater than or equal to"),
        openapi.definitions.Parameter(name="created_at__lte", location="query",
                                      type="string", description="Created at less than or equal to"),
        openapi.definitions.Parameter(
            name="updated_at", location="query", type="string", description="Updated at, format is ISO 8601"),
        openapi.definitions.Parameter(
            name="updated_at__gte", location="query",
            type="string", description="Updated at greater than or equal to"),
        openapi.definitions.Parameter(
            name="updated_at__lte", location="query",
            type="string", description="Updated at less than or equal to"),
        openapi.definitions.Parameter(
            name="order_by", location="query", type="string", description="Order by, default is created_at"),
    ],
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully retrieved a list of cover letters",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer", "description": "Error code, 0 means success"},
                            "message": {"type": "string", "description": "Response message"},
                            "data": {
                                "type": "object",
                                "description": "Response data",
                                "properties": {
                                    "total": {"type": "integer", "description": "Total number of records"},
                                    "page": {"type": "integer", "description": "Current page number"},
                                    "page_size": {"type": "integer", "description": "Number of records per page"},
                                    "records": {
                                        "type": "array",
                                        "description": "List of cover letters",
                                        "items": {
                                            "$ref": "#/components/schemas/ResponseCoverLetterSchema"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
@openapi.tag("Cover Letter", "Current User")
@require_token
async def get_list(request):
    """Get a paginated list of cover letters."""
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query_args = request.args
    query_args['user_id'] = request.ctx.user['id']
    query = CoverLetterQuery(**query_args)
    records, total = await CoverLetterRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@bp.route("/profile", methods=["GET"], strict_slashes=False)
@openapi.definition(
    summary="Get current user",
    description="This endpoint is used to get current user.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
    ],
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully retrieved current user",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer"},
                            "message": {"type": "string"},
                            "data": {
                                "type": "object",
                                "$ref": "#/components/schemas/ResponseUserSchema",
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_NOT_FOUND_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ],
)
@openapi.tag("User", "Current User")
@require_token
async def get_me(request):
    if request.ctx.user:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=dict(request.ctx.user)), 200)
    return json_response(Response(error=ResponseCode.GET_ONE_FAILED, message="user not found"), 404)


@bp.route("/saved-jobs", methods=["GET"], strict_slashes=False)
# @openapi.definition(
#     summary="Get all saved jobs for current user",
#     description="This endpoint is used to get all saved jobs for the current user.",
#     parameter=[
#         openapi.definitions.Parameter(
#             name="Authorization", location="header", type="string", description="Authorization header", required=True,
#             schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
#         openapi.definitions.Parameter(
#             name="page", location="query", type="integer", description="Page number, default is 1"),
#         openapi.definitions.Parameter(
#             name="page_size", location="query", type="integer",
#             description="Number of records per page, default is 10"),
#     ],
#     response=[
#         openapi.definitions.Response(
#             status=200, description="Successfully retrieved saved jobs for current user",
#             content={
#                 "application/json": {
#                     "schema": {
#                         "type": "object",
#                         "properties": {
#                             "error": {"type": "integer"},
#                             "message": {"type": "string"},
#                             "data": {
#                                 "type": "object",
#                                 "properties": {
#                                     "total": {"type": "integer", "description": "Total number of records"},
#                                     "page": {"type": "integer", "description": "Current page number"},
#                                     "page_size": {"type": "integer", "description": "Number of records per page"},
#                                     "records": {
#                                         "type": "array",
#                                         "items": {
#                                             "type": "object",
#                                             "$ref": "#/components/schemas/ResponseUserSavedJobSchema"
#                                         }
#                                     }
#                                 }
#                             }
#                         }
#                     }
#                 }
#             }
#         ),
#         OPENAPI_UNAUTHORIZED_RESPONSE,
#     ],
# )
# @openapi.tag("UserSavedJob", "Current User")
@require_token
async def get_my_saved_jobs(request):
    """
    Get all saved jobs for the current user.
    """
    try:
        # Get pagination parameters
        pagination = Pagination(
            page=int(request.args.get('page', 1)),
            page_size=int(request.args.get('page_size', 10))
        )

        # Get saved jobs for current user
        user_id = request.ctx.user['id']
        saved_jobs, total = await UserSavedJobRepository.get_user_saved_jobs(user_id, pagination)

        response = Response(
            error=ResponseCode.SUCCESS,
            message=SUCCESS_MESSAGE,
            data=ListResponse(
                total=total,
                page=pagination.page,
                page_size=pagination.page_size,
                records=saved_jobs
            )
        )
        return json_response(response, 200)
    except Exception as e:
        error_logger.exception("Error getting saved jobs for current user: %s", str(e))
        response = Response(
            error=ResponseCode.GET_LIST_FAILED,
            message=str(e),
            data={}
        )
        return json_response(response, 400)


@bp.route("/saved-jobs/<job_id:str>", methods=["POST"], strict_slashes=False)
# @openapi.definition(
#     summary="Save a job for current user",
#     description="This endpoint is used to save a job for the current user.",
#     parameter=[
#         openapi.definitions.Parameter(
#             name="Authorization", location="header", type="string", description="Authorization header", required=True,
#             schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
#         openapi.definitions.Parameter(
#             name="job_id", location="path", type="string", description="Job ID to save", required=True,
#             schema=openapi.definitions.Schema(type="string", format="UUID")),
#     ],
#     response=[
#         openapi.definitions.Response(
#             status=201, description="Successfully saved a job",
#             content={
#                 "application/json": {
#                     "schema": {
#                         "type": "object",
#                         "properties": {
#                             "error": {"type": "integer", "description": "Error code, 0 means success"},
#                             "message": {"type": "string", "description": "Response message"},
#                             "data": {
#                                 "type": "object",
#                                 "$ref": "#/components/schemas/ResponseUserSavedJobSchema",
#                             }
#                         }
#                     }
#                 }
#             }
#         ),
#         OPENAPI_BAD_REQUEST_RESPONSE,
#         OPENAPI_UNAUTHORIZED_RESPONSE,
#     ],
# )
# @openapi.tag("UserSavedJob", "Current User")
@require_token
async def save_job(request, job_id: str):
    """
    Save a job for the current user and create a job description from it.
    """
    try:
        # Get user ID from token
        user_id = request.ctx.user['id']

        # Create saved job data
        saved_job_data = {
            "user_id": user_id,
            "job_id": job_id
        }

        # Create saved job
        saved_job = await UserSavedJobRepository.create_one(saved_job_data)

        try:
            # Get the job data from SearchJob
            job_data = await SearchJobRepository.get_one(job_id)
            if job_data:
                # Map the job data to JobDescription fields
                job_description_data = {
                    "user_id": user_id,
                    "description": job_data.get("description", ""),
                    "position": job_data.get("title"),
                    "company": job_data.get("company"),
                    # Extract city and country from location if available
                    "city": job_data.get("location"),  # This is simplified, might need parsing
                    "country": None,  # Would need parsing from location
                    # Try to convert posted_at to datetime if it exists
                    "posted_at": datetime.now() if job_data.get("posted_at") else None,

                    "salary": job_data.get("salary"),
                    "link": job_data.get("link"),
                    "is_remote": job_data.get("is_remote", False),
                    "source": job_data.get("source"),
                    "category": job_data.get("category"),
                    "keywords": job_data.get("keywords", []),
                    "skills": [],  # No direct mapping, would need extraction from description
                    "status": "private",  # Using string value instead of enum to avoid import issues
                    # Contract type would need to be determined from job data
                    "contract_type": None,
                    "is_favorite": True,  # Since user is saving it
                    "promotion": "normal"  # Using string value instead of enum to avoid import issues
                }

                # Create job description
                await JobDescriptionRepository.create_one(job_description_data)
                error_logger.info(f"Created job description from saved job {job_id} for user {user_id}")
            else:
                error_logger.warning(f"Could not find job with ID {job_id} to create job description")
        except Exception as e:
            # Log the error but don't fail the entire request
            error_logger.exception(f"Error creating job description from saved job: {str(e)}")

        return json_response(
            Response(error=ResponseCode.SUCCESS, message=SUCCESS_MESSAGE, data=saved_job),
            201
        )
    except Exception as e:
        error_logger.exception("Error saving job: %s", str(e))
        return json_response(
            Response(error=ResponseCode.CREATE_FAILED, message=str(e), data={}),
            400
        )


@bp.route("/saved-jobs/<job_id:str>", methods=["DELETE"], strict_slashes=False)
@openapi.definition(
    summary="Delete a saved job for current user",
    description="This endpoint is used to delete a saved job for the current user.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            name="job_id", location="path", type="string", description="Job ID to delete", required=True,
            schema=openapi.definitions.Schema(type="string", format="UUID")),
    ],
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully deleted a saved job",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer"},
                            "message": {"type": "string"},
                            "data": {"type": "object"}
                        }
                    }
                }
            }
        ),
        OPENAPI_NOT_FOUND_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ],
)
@openapi.tag("UserSavedJob", "Current User")
@require_token
async def delete_saved_job(request, job_id: str):
    """
    Delete a saved job for the current user.
    """
    try:
        # Get user ID from token
        user_id = request.ctx.user['id']

        # Delete saved job
        count, _ = await UserSavedJobRepository.delete(user_id, job_id)

        if count == 0:
            return json_response(
                Response(error=ResponseCode.GET_ONE_FAILED, message="Saved job not found", data={}),
                404
            )

        return json_response(
            Response(error=ResponseCode.SUCCESS, message=SUCCESS_MESSAGE, data={}),
            200
        )
    except Exception as e:
        error_logger.exception("Error deleting saved job: %s", str(e))
        return json_response(
            Response(error=ResponseCode.DELETE_FAILED, message=str(e), data={}),
            400
        )
