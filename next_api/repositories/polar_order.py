import json
from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, ConfigDict, field_validator
from models.polar_order import PolarOrder
from repositories.base import Pagination, OrderBy
from utils.cache import CACHE_MANAGER
from utils.helpers import ExtendedJsonEncoder


class PolarOrderOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "user_id", "-user_id",
        "user_email", "-user_email",
        "order_id", "-order_id",
        "product_id", "-product_id",
        "currency", "-currency",
        "total_amount", "-total_amount",
        "paid", "-paid",
        "status", "-status",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
    )
    _default_order_by = ("-created_at",)


class PolarOrderQuery(BaseModel):
    model_config = ConfigDict(extra="forbid")

    # ID filters
    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    # User filters
    user_id: Optional[UUID] = None
    user_id__in: Optional[List[UUID]] = None
    user_email: Optional[str] = None
    user_email__icontains: Optional[str] = None

    # Order filters
    order_id: Optional[str] = None
    order_id__in: Optional[List[str]] = None
    product_id: Optional[str] = None
    product_id__in: Optional[List[str]] = None

    # Subscription plan filters
    subscription_plan_id: Optional[UUID] = None
    subscription_plan_id__in: Optional[List[UUID]] = None

    # Currency filters
    currency: Optional[str] = None
    currency__in: Optional[List[str]] = None

    # Amount filters
    total_amount: Optional[Decimal] = None
    total_amount__gt: Optional[Decimal] = None
    total_amount__gte: Optional[Decimal] = None
    total_amount__lt: Optional[Decimal] = None
    total_amount__lte: Optional[Decimal] = None

    # Boolean filters
    paid: Optional[bool] = None

    # Status filters
    status: Optional[int] = None
    status__in: Optional[List[int]] = None
    status__not_in: Optional[List[int]] = None

    # Date filters
    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    # Ordering
    order_by: Optional[Union[str, List[str]]] = None

    @field_validator(
        "id", "user_id", "subscription_plan_id",
        mode="before"
    )
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        return v

    @field_validator(
        "id__in", "id__not_in", "user_id__in", "subscription_plan_id__in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> List[UUID]:
        if isinstance(v, str):
            return [UUID(x.strip()) for x in v.split(",") if x.strip()]
        return v

    @field_validator("user_email", "user_email__icontains",
                     "order_id", "product_id", "currency", mode="before")
    @classmethod
    def validate_string(cls, v: Any) -> str:
        if isinstance(v, list):
            return str(v[0])
        return str(v)

    @field_validator(
        "order_id__in", "product_id__in", "currency__in",
        mode="before"
    )
    @classmethod
    def validate_string_list(cls, v: Any) -> List[str]:
        if isinstance(v, str):
            return [x.strip() for x in v.split(",") if x.strip()]
        return v

    @field_validator(
        "total_amount", "total_amount__gt", "total_amount__gte",
        "total_amount__lt", "total_amount__lte",
        mode="before"
    )
    @classmethod
    def validate_decimal(cls, v: Any) -> Decimal:
        if isinstance(v, str):
            return Decimal(v)
        return v

    @field_validator(
        "status",
        mode="before"
    )
    @classmethod
    def validate_int(cls, v: Any) -> int:
        if isinstance(v, str):
            return int(v)
        return v

    @field_validator(
        "status__in", "status__not_in",
        mode="before"
    )
    @classmethod
    def validate_int_list(cls, v: Any) -> List[int]:
        if isinstance(v, str):
            return [int(x) for x in v.split(",")]
        return v

    @field_validator(
        "paid",
        mode="before"
    )
    @classmethod
    def validate_bool(cls, v: Any) -> bool:
        if isinstance(v, str):
            return v.lower() in ("true", "1", "yes", "on")
        return bool(v)


class PolarOrderRepository:
    POLAR_ORDER_CACHE_KEY = 'polar_order:{{{order_id}}}'
    CACHE_EXPIRY = 3600  # 1 hour

    @staticmethod
    async def create_one(order: Dict[str, Any]) -> dict:
        """Create a single polar order record."""
        record = await PolarOrder.create(**order)
        return dict(record)

    @staticmethod
    async def create_many(orders: List[Dict[str, Any]]) -> List[PolarOrder]:
        """Create multiple polar order records in bulk."""
        records = await PolarOrder.bulk_create([PolarOrder(**order) for order in orders])
        return records

    @staticmethod
    async def get_one(pk: Union[str, UUID]) -> Optional[dict]:
        """Get a single polar order by ID."""
        if isinstance(pk, str):
            pk = UUID(pk)

        # Try to get from cache first
        redis = CACHE_MANAGER.get_connection('default')
        if redis:
            cache_key = PolarOrderRepository.POLAR_ORDER_CACHE_KEY.format(
                order_id=str(pk))
            cached_data = redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)

        # If cache miss, get from database
        order = await PolarOrder.get_or_none(id=pk).values()

        # Cache the result if found
        if order and redis:
            cache_key = PolarOrderRepository.POLAR_ORDER_CACHE_KEY.format(
                order_id=str(pk))
            redis.setex(
                cache_key,
                PolarOrderRepository.CACHE_EXPIRY,
                json.dumps(
                    order,
                    cls=ExtendedJsonEncoder))

        return order

    @staticmethod
    async def get_by_order_id(order_id: str) -> Optional[dict]:
        """Get a single polar order by order_id."""
        order = await PolarOrder.get_or_none(order_id=order_id).values()
        return order

    @staticmethod
    async def get_list(q: PolarOrderQuery,
                       p: Pagination) -> Tuple[List[dict], int]:
        """Get a paginated list of polar orders matching the query criteria."""
        query = PolarOrder.all()

        # Apply filters
        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)

        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_email is not None:
            query = query.filter(user_email=q.user_email)
        if q.user_email__icontains is not None:
            query = query.filter(user_email__icontains=q.user_email__icontains)

        if q.order_id is not None:
            query = query.filter(order_id=q.order_id)
        if q.order_id__in is not None:
            query = query.filter(order_id__in=q.order_id__in)
        if q.product_id is not None:
            query = query.filter(product_id=q.product_id)
        if q.product_id__in is not None:
            query = query.filter(product_id__in=q.product_id__in)

        if q.subscription_plan_id is not None:
            query = query.filter(subscription_plan_id=q.subscription_plan_id)
        if q.subscription_plan_id__in is not None:
            query = query.filter(
                subscription_plan_id__in=q.subscription_plan_id__in)

        if q.currency is not None:
            query = query.filter(currency=q.currency)
        if q.currency__in is not None:
            query = query.filter(currency__in=q.currency__in)

        if q.total_amount is not None:
            query = query.filter(total_amount=q.total_amount)
        if q.total_amount__gt is not None:
            query = query.filter(total_amount__gt=q.total_amount__gt)
        if q.total_amount__gte is not None:
            query = query.filter(total_amount__gte=q.total_amount__gte)
        if q.total_amount__lt is not None:
            query = query.filter(total_amount__lt=q.total_amount__lt)
        if q.total_amount__lte is not None:
            query = query.filter(total_amount__lte=q.total_amount__lte)

        if q.paid is not None:
            query = query.filter(paid=q.paid)

        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)

        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        total = await query.count()
        order_by = PolarOrderOrderBy(q.order_by)
        orders = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).all().values()

        return orders, total

    @staticmethod
    async def update(
            pk: Union[str, UUID], data: Dict[str, Any]) -> Tuple[int, Optional[dict]]:
        """Update a polar order record."""
        if isinstance(pk, str):
            pk = UUID(pk)
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()

        affected_rows = await PolarOrder.filter(id=pk).update(**data)
        updated_order = None
        if affected_rows > 0:
            updated_order = await PolarOrder.get(id=pk).values()
            # Update cache
            redis = CACHE_MANAGER.get_connection('default')
            if redis:
                cache_key = PolarOrderRepository.POLAR_ORDER_CACHE_KEY.format(
                    order_id=str(pk))
                redis.setex(
                    cache_key,
                    PolarOrderRepository.CACHE_EXPIRY,
                    json.dumps(
                        updated_order,
                        cls=ExtendedJsonEncoder))

        return affected_rows, updated_order

    @staticmethod
    async def delete(pk: Union[str, UUID]) -> int:
        """Delete a polar order record."""
        if isinstance(pk, str):
            pk = UUID(pk)

        # Remove from cache
        redis = CACHE_MANAGER.get_connection('default')
        if redis:
            cache_key = PolarOrderRepository.POLAR_ORDER_CACHE_KEY.format(
                order_id=str(pk))
            redis.delete(cache_key)

        affected_rows = await PolarOrder.filter(id=pk).delete()
        return affected_rows

    @staticmethod
    async def get_by_user_id(
            user_id: Union[str, UUID], p: Pagination) -> Tuple[List[dict], int]:
        """Get all polar orders for a specific user."""
        if isinstance(user_id, str):
            user_id = UUID(user_id)

        query = PolarOrder.filter(user_id=user_id)
        total = await query.count()
        orders = await query.order_by("-created_at").offset(p.offset).limit(p.limit).values()

        return orders, total

    @staticmethod
    async def get_by_product_id(
            product_id: str, p: Pagination) -> Tuple[List[dict], int]:
        """Get all polar orders for a specific product."""
        query = PolarOrder.filter(product_id=product_id)
        total = await query.count()
        orders = await query.order_by("-created_at").offset(p.offset).limit(p.limit).values()

        return orders, total
