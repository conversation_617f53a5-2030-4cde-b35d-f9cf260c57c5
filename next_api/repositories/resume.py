from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, ConfigDict, field_validator
from tortoise.expressions import RawSQL
from models.resume import Resume, ResumeEducation, ResumeExperience
from repositories.base import Pagination, OrderBy


class ResumeOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "title", "-title",
        "status", "-status",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
        "ats_score", "-ats_score",
        "keyword_score", "-keyword_score",
        "structure_score", "-structure_score",
        "content_quality_score", "-content_quality_score",
    )
    _default_order_by = ("created_at",)


class ResumeQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    user_id: Optional[UUID] = None
    user_id__in: Optional[List[UUID]] = None
    user_id__not_in: Optional[List[UUID]] = None

    title: Optional[str] = None
    title__contains: Optional[str] = None
    title__icontains: Optional[str] = None
    title__startswith: Optional[str] = None
    title__endswith: Optional[str] = None

    content: Optional[str] = None
    content__contains: Optional[str] = None
    content__icontains: Optional[str] = None

    keywords__contains: Optional[List[str]] = None

    status: Optional[Resume.Status] = None
    status__in: Optional[List[Resume.Status]] = None
    status__not_in: Optional[List[Resume.Status]] = None

    # ATS scoring fields
    ats_score: Optional[int] = None
    ats_score__gte: Optional[int] = None
    ats_score__lte: Optional[int] = None

    keyword_score: Optional[float] = None
    keyword_score__gte: Optional[float] = None
    keyword_score__lte: Optional[float] = None

    structure_score: Optional[float] = None
    structure_score__gte: Optional[float] = None
    structure_score__lte: Optional[float] = None

    content_quality_score: Optional[float] = None
    content_quality_score__gte: Optional[float] = None
    content_quality_score__lte: Optional[float] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    @field_validator(
        "id", "user_id",
        mode="before"
    )
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator(
        "id__in", "id__not_in",
        "user_id__in", "user_id__not_in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(',')]
        return v

    @field_validator(
        "title", "title__contains", "title__icontains", "title__startswith", "title__endswith",
        "content", "content__contains", "content__icontains",
        mode="before"
    )
    @classmethod
    def validate_string(cls, v: Any) -> Any:
        if isinstance(v, list):
            return v[0]
        return v

    @field_validator(
        "keywords__contains",
        mode="before"
    )
    @classmethod
    def validate_str_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return v.split(',')
        if isinstance(v, list):
            result = []
            for item in v:
                result.extend(item.split(','))
            return result
        return v

    @field_validator(
        "status",
        mode="before"
    )
    @classmethod
    def validate_int(cls, v: Any) -> int:
        if isinstance(v, str):
            v = int(v)
        elif isinstance(v, list):
            v = int(v[0])
        return v

    @field_validator(
        "status__in", "status__not_in",
        mode="before"
    )
    @classmethod
    def validate_int_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [int(v) for v in v.split(',')]
        return v

    @field_validator(
        "ats_score", "ats_score__gte", "ats_score__lte",
        mode="before"
    )
    @classmethod
    def validate_ats_score(cls, v: Any) -> Optional[int]:
        if isinstance(v, str):
            return int(v)
        if isinstance(v, list):
            return int(v[0])
        return v

    @field_validator(
        "keyword_score", "keyword_score__gte", "keyword_score__lte",
        "structure_score", "structure_score__gte", "structure_score__lte",
        "content_quality_score", "content_quality_score__gte", "content_quality_score__lte",
        mode="before"
    )
    @classmethod
    def validate_float_score(cls, v: Any) -> Optional[float]:
        if isinstance(v, str):
            return float(v)
        if isinstance(v, list):
            return float(v[0])
        return v

    @field_validator(
        "created_at", "created_at__gte", "created_at__lte",
        "updated_at", "updated_at__gte", "updated_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        return datetime.fromisoformat(v)


class ResumeRepository:

    @staticmethod
    async def create_one(resume: Dict[str, Any]) -> Resume:
        """Create a single resume record from a dictionary of resume data."""
        return await Resume.create(**resume)

    @staticmethod
    async def create_many(resumes: List[Dict[str, Any]]) -> List[Resume]:
        """Create multiple resume records in bulk."""
        return await Resume.bulk_create([Resume(**resume) for resume in resumes])

    @staticmethod
    async def get_one(q: ResumeQuery) -> Optional[Dict[str, Any]]:
        """Get a single resume that matches the ID in the query."""
        resume = await Resume \
            .get_or_none(id=q.id) \
            .values()
        return resume

    @staticmethod
    async def get_list(q: ResumeQuery, p: Pagination) -> Tuple[Union[List[dict], List[Resume]], int]:
        """Get a paginated list of resumes matching the query criteria."""
        query = Resume.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_id__not_in is not None:
            query = query.filter(user_id__not_in=q.user_id__not_in)
        if q.title is not None:
            query = query.filter(title=q.title)
        if q.title__contains is not None:
            query = query.filter(title__contains=q.title__contains)
        if q.title__icontains is not None:
            query = query.filter(title__icontains=q.title__icontains)
        if q.title__startswith is not None:
            query = query.filter(title__startswith=q.title__startswith)
        if q.title__endswith is not None:
            query = query.filter(title__endswith=q.title__endswith)
        if q.content__contains is not None:
            query = query.filter(content__contains=q.content__contains)
        if q.content__icontains is not None:
            query = query.filter(content__icontains=q.content__icontains)
        if q.keywords__contains is not None:
            query = query.annotate(keywords_contains=RawSQL(
                "keywords @> ARRAY[" + ",".join([f"'{keyword}'" for keyword in q.keywords__contains]) + "]::TEXT[]"))
            query = query.filter(keywords_contains=True)
        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)
        if q.ats_score is not None:
            query = query.filter(ats_score=q.ats_score)
        if q.ats_score__gte is not None:
            query = query.filter(ats_score__gte=q.ats_score__gte)
        if q.ats_score__lte is not None:
            query = query.filter(ats_score__lte=q.ats_score__lte)
        if q.keyword_score is not None:
            query = query.filter(keyword_score=q.keyword_score)
        if q.keyword_score__gte is not None:
            query = query.filter(keyword_score__gte=q.keyword_score__gte)
        if q.keyword_score__lte is not None:
            query = query.filter(keyword_score__lte=q.keyword_score__lte)
        if q.structure_score is not None:
            query = query.filter(structure_score=q.structure_score)
        if q.structure_score__gte is not None:
            query = query.filter(structure_score__gte=q.structure_score__gte)
        if q.structure_score__lte is not None:
            query = query.filter(structure_score__lte=q.structure_score__lte)
        if q.content_quality_score is not None:
            query = query.filter(content_quality_score=q.content_quality_score)
        if q.content_quality_score__gte is not None:
            query = query.filter(content_quality_score__gte=q.content_quality_score__gte)
        if q.content_quality_score__lte is not None:
            query = query.filter(content_quality_score__lte=q.content_quality_score__lte)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        total = await query.count()
        order_by = ResumeOrderBy(q.order_by)
        resumes = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).all().values()

        return resumes, total

    @staticmethod
    async def update(pk: Union[str, UUID], data: Union[dict, Resume]) -> Tuple[int, Optional[Dict[str, Any]]]:
        """Update a resume record."""
        if isinstance(pk, str):
            pk = UUID(pk)
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()
        if isinstance(data, dict):
            affected_rows = await Resume.filter(id=pk).update(**data)
        else:
            affected_rows = await Resume.filter(id=pk).update(data)

        updated_resume = None
        if affected_rows > 0:
            updated_resume = await Resume.get(pk=pk).values()

        return affected_rows, updated_resume

    @staticmethod
    async def update_by_query(update_data: dict, q: ResumeQuery) -> int:
        """Update multiple resume records that match the given query criteria."""
        query = Resume.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_id__not_in is not None:
            query = query.filter(user_id__not_in=q.user_id__not_in)
        if q.title is not None:
            query = query.filter(title=q.title)
        if q.title__contains is not None:
            query = query.filter(title__contains=q.title__contains)
        if q.title__icontains is not None:
            query = query.filter(title__icontains=q.title__icontains)
        if q.title__startswith is not None:
            query = query.filter(title__startswith=q.title__startswith)
        if q.title__endswith is not None:
            query = query.filter(title__endswith=q.title__endswith)
        if q.content__contains is not None:
            query = query.filter(content__contains=q.content__contains)
        if q.content__icontains is not None:
            query = query.filter(content__icontains=q.content__icontains)
        if q.keywords__contains is not None:
            query = query.annotate(keywords_contains=RawSQL(
                "keywords @> ARRAY[" + ",".join([f"'{keyword}'" for keyword in q.keywords__contains]) + "]::TEXT[]"))
            query = query.filter(keywords_contains=True)
        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)

        if 'updated_at' not in update_data:
            update_data['updated_at'] = datetime.now()
        return await query.update(**update_data)

    @staticmethod
    async def create_educations(resume: Resume, educations: List[Dict[str, Any]]) -> bool:
        """Create multiple education records for a resume."""
        if resume is not None and educations:
            l = []
            for education in educations:
                education['resume'] = resume
                l.append(ResumeEducation(**education))
            await ResumeEducation.bulk_create(l)
            return True
        return False

    @ staticmethod
    async def get_education(pk: Union[str, UUID]) -> Optional[ResumeEducation]:
        """Get an education record by ID."""
        return await ResumeEducation.get_or_none(id=pk).values()

    @ staticmethod
    async def update_education(pk: Union[str, UUID], data: Union[dict, ResumeEducation]) \
            -> Tuple[int, Optional[Dict[str, Any]]]:
        """Update a resume education record."""
        if isinstance(pk, str):
            pk = UUID(pk)
        if isinstance(data, dict):
            affected_rows = await ResumeEducation.filter(id=pk).update(**data)
        else:
            affected_rows = await ResumeEducation.filter(id=pk).update(data)

        updated_record = None
        if affected_rows > 0:
            updated_record = await ResumeEducation.get(pk=pk).values()

        return affected_rows, updated_record

    @staticmethod
    async def delete_education(pk: Union[str, UUID]) -> int:
        """Delete a resume education record."""
        return await ResumeEducation.filter(id=pk).delete()

    @staticmethod
    async def create_experiences(resume: Resume, experiences: List[Dict[str, Any]]) -> bool:
        """Create multiple experience records for a resume."""
        if resume is not None and experiences:
            l = []
            for experience in experiences:
                experience['resume'] = resume
                l.append(ResumeExperience(**experience))
            await ResumeExperience.bulk_create(l)
            return True
        return False

    @staticmethod
    async def get_experience(pk: Union[str, UUID]) -> Optional[ResumeExperience]:
        """Get an experience record by ID."""
        return await ResumeExperience.get_or_none(id=pk).values()

    @staticmethod
    async def update_experience(pk: Union[str, UUID], data: Union[dict, ResumeExperience]) \
            -> Tuple[int, Optional[Dict[str, Any]]]:
        """Update a resume experience record."""
        if isinstance(pk, str):
            pk = UUID(pk)
        if isinstance(data, dict):
            affected_rows = await ResumeExperience.filter(id=pk).update(**data)
        else:
            affected_rows = await ResumeExperience.filter(id=pk).update(data)

        updated_record = None
        if affected_rows > 0:
            updated_record = await ResumeExperience.get(pk=pk).values()

        return affected_rows, updated_record

    @staticmethod
    async def delete_experience(pk: Union[str, UUID]) -> int:
        """Delete a resume experience record."""
        return await ResumeExperience.filter(id=pk).delete()
