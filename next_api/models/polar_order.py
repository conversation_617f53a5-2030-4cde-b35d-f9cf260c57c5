from enum import IntEnum
from tortoise.models import Model
from tortoise.fields import (
    UUIDField,
    ForeignKeyField,
    TextField,
    DecimalField,
    BooleanField,
    SmallIntField,
    DatetimeField,
    ForeignKeyRelation
)
from uuid_extensions import uuid7


class PolarOrder(Model):
    """
    Model representing a Polar order in the system.
    """
    class Meta:
        table = "polar_order"
        schema = "public"
        ordering = ["-created_at"]

    class Status(IntEnum):
        UNKNOWN = 0
        PENDING = 1
        PAID = 2
        REFUNDED = 3
        PARTIALLY_REFUNDED = 4

    id = UUIDField(pk=True, default=uuid7)
    user_id = UUIDField(null=True)
    user_email = TextField(null=True)
    order_id = TextField(null=True)
    product_id = TextField(null=True)
    subscription_plan: ForeignKeyRelation["SubscriptionPlan"] = ForeignKeyField(
        "next_api.SubscriptionPlan", related_name="polar_orders", null=True)
    currency = TextField(default="USD")
    subtotal_amount = DecimalField(max_digits=18, decimal_places=2, default=0)
    discount_amount = DecimalField(max_digits=18, decimal_places=2, default=0)
    net_amount = DecimalField(max_digits=18, decimal_places=2, default=0)
    amount = DecimalField(max_digits=18, decimal_places=2, default=0)
    tax_amount = DecimalField(max_digits=18, decimal_places=2, default=0)
    total_amount = DecimalField(max_digits=18, decimal_places=2, default=0)
    refunded_amount = DecimalField(max_digits=18, decimal_places=2, default=0)
    refunded_tax_amount = DecimalField(
        max_digits=18, decimal_places=2, default=0)
    paid = BooleanField(default=False)
    status = SmallIntField(default=Status.UNKNOWN)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)

    def __str__(self):
        return f"PolarOrder {self.order_id} - {self.user_email}"
