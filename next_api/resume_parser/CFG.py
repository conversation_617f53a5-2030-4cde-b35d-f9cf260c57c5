from typing import List, Optional
from pydantic import BaseModel, Field

class UrlInfo(BaseModel):
    label: str = Field(default="", description="Label of the URL, default: empty string")
    href: str = Field(default="", description="URL link, default: empty string")

class CustomField(BaseModel):
    id: str = Field(default="CustomField", description="Unique UUID identifier for the custom field")
    icon: str = Field(default="CustomField", description="Name of the icon for the custom field")
    name: str = Field(default="CustomField", description="Name of the custom field")
    value: str = Field(default="CustomField", description="Value of the custom field")

class PictureEffects(BaseModel):
    hidden: bool = Field(default=False, description="Whether the profile picture is hidden")
    border: bool = Field(default=False, description="Whether the profile picture has a border")
    grayscale: bool = Field(default=False, description="Whether the profile picture is grayscale")

class Picture(BaseModel):
    url: str = Field(default="https://i.imgur.com/HgwyOuJ.jpg", description="URL of the profile picture")
    size: int = Field(default=64, description="Size of the profile picture")
    aspectRatio: int = Field(default=1, description="Aspect ratio of the profile picture")
    borderRadius: int = Field(default=0, description="Border radius of the profile picture")
    effects: PictureEffects = Field(default_factory=lambda: PictureEffects(), description="Effects applied to the profile picture")

class Basics(BaseModel):
    fullname: str = Field(default="NextCareer", description="The person's full name")
    headline: str = Field(default="NextCareer", description="Professional headline or title")
    email: str = Field(default="", description="Email address")
    phone: str = Field(default="NextCareer", description="Phone number with format")
    location: str = Field(default="NextCareer", description="City, State, ZIP code")
    url: UrlInfo = Field(default_factory=lambda: UrlInfo(), description="Website information")
    customFields: List[CustomField] = Field(default_factory=list, description="Custom fields")
    picture: Picture = Field(default_factory=lambda: Picture(), description="Profile picture information")

class SectionBase(BaseModel):
    name: str = Field(default="Section", description="Name of the section")
    columns: int = Field(default=1, description="Number of columns, must be greater than 0")
    separateLinks: bool = Field(default=True, description="Whether to separate links")
    visible: bool = Field(default=True, description="Whether to show the section")
    id: str = Field(default="SectionBase", description="Unique identifier for the section")

class SummarySection(SectionBase):
    id: str = Field(default="summary", description="Unique identifier for the section")
    content: str = Field(default="Summary", description="Content of the section")

class SkillItem(BaseModel):
    name: str = Field(default="Skill", description="Skill category")
    description: str = Field(default="Skill", description="Proficiency level")
    keywords: List[str] = Field(default_factory=list, description="Specific skills")
    level: int = Field(default=0, description="Level of the skill")
    visible: bool = Field(default=True, description="Whether to show the item")

class SkillsSection(SectionBase):
    name: str = Field(default="SKILLS", description="Name of the section")
    columns: int = Field(default=1, description="Number of columns, must be greater than 0")
    separateLinks: bool = Field(default=False, description="Whether to separate links")
    visible: bool = Field(default=False, description="Whether to show the section")
    id: str = Field(default="skills", description="Unique identifier for the section")
    items: List[SkillItem] = Field(default_factory=list, description="List of skills")

class ExperienceItem(BaseModel):
    company: str = Field(default="company", description="Company name")
    position: str = Field(default="position", description="Job title")
    location: str = Field(default="location", description="Job location")
    date: str = Field(default="date", description="Employment period")
    summary: str = Field(default="summary", description="Job description and achievements")
    url: UrlInfo = Field(default_factory=lambda: UrlInfo(), description="URL of the experience")
    visible: bool = Field(default=True, description="Whether to show the item")

class ExperienceSection(SectionBase):
    id: str = Field(default="experience", description="Unique identifier for the section")
    items: List[ExperienceItem] = Field(default_factory=list, description="List of experiences")

class EducationItem(BaseModel):
    institution: str = Field(default="institution", description="School or university name")
    studyType: str = Field(default="studyType", description="Degree type")
    area: str = Field(default="area", description="Field of study")
    date: str = Field(default="date", description="Education period")
    summary: str = Field(default="Summary", description="Summary of the education")
    url: UrlInfo = Field(default_factory=lambda: UrlInfo(), description="URL of the education")
    visible: bool = Field(default=True, description="Whether to show the item")
    score: str = Field(default="4.0", description="Score of the education")

class EducationSection(SectionBase):
    id: str = Field(default="education", description="Unique identifier for the section")
    items: List[EducationItem] = Field(default_factory=list, description="List of education")

class CertificationItem(BaseModel):
    name: str = Field(default="Certification", description="Certification name")
    issuer: str = Field(default="Issuer", description="Issuing organization")
    date: str = Field(default="Date", description="Date obtained")
    visible: bool = Field(default=True, description="Whether to show the item")
    summary: str = Field(default="Summary", description="Summary of the certification")
    url: UrlInfo = Field(default_factory=lambda: UrlInfo(), description="URL of the certification")

class CertificationsSection(SectionBase):
    id: str = Field(default="certifications", description="Unique identifier for the section")
    items: List[CertificationItem] = Field(default_factory=list, description="List of certifications")

class ProjectItem(BaseModel):
    name: str = Field(default="Project", description="Project name")
    description: str = Field(default="Description", description="Role or project type")
    summary: str = Field(default="Summary", description="Project description and achievements")
    date: str = Field(default="Date", description="Project period")
    keywords: List[str] = Field(default_factory=list, description="Specific keywords")
    url: UrlInfo = Field(default_factory=lambda: UrlInfo(), description="URL of the project")
    visible: bool = Field(default=True, description="Whether to show the item")

class ProjectsSection(SectionBase):
    id: str = Field(default="projects", description="Unique identifier for the section")
    items: List[ProjectItem] = Field(default_factory=list, description="List of projects")

class PublicationItem(BaseModel):
    name: str = Field(default="Publication", description="Publication title")
    publisher: str = Field(default="Publisher", description="Publisher name")
    date: str = Field(default="Date", description="Publication date")
    url: UrlInfo = Field(default_factory=lambda: UrlInfo(), description="URL of the publication")
    summary: str = Field(default="Summary", description="Publication description")
    visible: bool = Field(default=True, description="Whether to show the item")

class PublicationsSection(SectionBase):
    id: str = Field(default="publications", description="Unique identifier for the section")
    items: List[PublicationItem] = Field(default_factory=list, description="List of publications")

class VolunteerItem(BaseModel):
    organization: str = Field(default="Organization", description="Organization name", validate_default=True)
    position: str = Field(default="Position", description="Volunteer position")
    date: str = Field(default="Date", description="Volunteer period")
    summary: str = Field(default="Summary", description="Description of volunteer work")
    location: str = Field(default="Location", description="Location of the volunteer")
    url: UrlInfo = Field(default_factory=lambda: UrlInfo(), description="URL of the volunteer")
    visible: bool = Field(default=True, description="Whether to show the item")

class VolunteerSection(SectionBase):
    id: str = Field(default="volunteer", description="Unique identifier for the section")
    items: List[VolunteerItem] = Field(default_factory=list, description="List of volunteer experiences")

class LanguageItem(BaseModel):
    name: str = Field(default="Language", description="Language name")
    description: str = Field(default="Description", description="Proficiency level")
    visible: bool = Field(default=True, description="Whether to show the item")

class LanguagesSection(SectionBase):
    id: str = Field(default="languages", description="Unique identifier for the section")
    items: List[LanguageItem] = Field(default_factory=list, description="List of languages")

class InterestItem(BaseModel):
    name: str = Field(default="Interest", description="Interest category")
    visible: bool = Field(default=True, description="Whether to show the item")
    keywords: List[str] = Field(default_factory=list, description="Specific interests")

class InterestsSection(SectionBase):
    id: str = Field(default="interests", description="Unique identifier for the section")
    items: List[InterestItem] = Field(default_factory=list, description="List of interests")

class AwardItem(BaseModel):
    title: str = Field(default="Award", description="Award name")
    awarder: str = Field(default="Awarder", description="Awarding organization")
    date: str = Field(default="Date", description="Date received")
    summary: str = Field(default="Summary for the award", description="Description of the award")
    visible: bool = Field(default=True, description="Whether to show the item")
    url: UrlInfo = Field(default_factory=lambda: UrlInfo(), description="URL of the award")

class AwardsSection(SectionBase):
    id: str = Field(default="awards", description="Unique identifier for the section")
    items: List[AwardItem] = Field(default_factory=list, description="List of awards")

class ReferenceItem(BaseModel):
    name: str = Field(default="Reference", description="Reference name or statement")
    visible: bool = Field(default=True, description="Whether to show the item")
    description: str = Field(default="Description", description="Description of the reference")
    summary: str = Field(default="Summary for the reference", description="Summary of the reference")
    url: UrlInfo = Field(default_factory=lambda: UrlInfo(), description="URL of the reference")

class ReferencesSection(SectionBase):
    id: str = Field(default="references", description="Unique identifier for the section")
    items: List[ReferenceItem] = Field(default_factory=list, description="List of references")

class ProfileItem(BaseModel):
    name: str = Field(default="Profile", description="Profile name")
    visible: bool = Field(default=True, description="Whether to show the item")
    network: str = Field(default="linkedin", description="Network of the profile")
    icon: str = Field(default="linkedin", description="Icon of the profile")
    username: str = Field(default="", description="Username or handle for the profile")
    url: UrlInfo = Field(default_factory=lambda: UrlInfo(), description="URL of the profile")

class ProfileSection(SectionBase):
    id: str = Field(default="profiles", description="Unique identifier for the section")
    name: str = Field(default="Profiles", description="Name of the section")
    columns: int = Field(default=1, description="Number of columns, must be greater than 0")
    separateLinks: bool = Field(default=True, description="Whether to separate links")
    visible: bool = Field(default=True, description="Whether to show the section")
    items: List[ProfileItem] = Field(default_factory=list, description="List of profiles")

class CustomItem(BaseModel):
    name: str = Field(default="Custom", description="Custom item name")
    visible: bool = Field(default=True, description="Whether to show the item")
    description: str = Field(default="Description", description="Description of the custom item")

class CustomSection(SectionBase):
    id: str = Field(default="custom", description="Unique identifier for the section")
    name: str = Field(default="Custom", description="Name of the section")
    columns: int = Field(default=1, description="Number of columns, must be greater than 0")
    separateLinks: bool = Field(default=True, description="Whether to separate links")
    visible: bool = Field(default=True, description="Whether to show the section")
    items: List[CustomItem] = Field(default_factory=list, description="List of custom items")

class Sections(BaseModel):
    summary: Optional[SummarySection] = Field(default_factory=lambda: SummarySection(), description="Summary section")
    skills: Optional[SkillsSection] = Field(default_factory=lambda: SkillsSection(), description="Skills section")
    experience: Optional[ExperienceSection] = Field(default_factory=lambda: ExperienceSection(), description="Experience section")
    education: Optional[EducationSection] = Field(default_factory=lambda: EducationSection(), description="Education section")
    certifications: Optional[CertificationsSection] = Field(default_factory=lambda: CertificationsSection(), description="Certifications section")
    projects: Optional[ProjectsSection] = Field(default_factory=lambda: ProjectsSection(), description="Projects section")
    publications: Optional[PublicationsSection] = Field(default_factory=lambda: PublicationsSection(), description="Publications section")
    volunteer: Optional[VolunteerSection] = Field(default_factory=lambda: VolunteerSection(), description="Volunteer section")
    languages: Optional[LanguagesSection] = Field(default_factory=lambda: LanguagesSection(), description="Languages section")
    interests: Optional[InterestsSection] = Field(default_factory=lambda: InterestsSection(), description="Interests section")
    awards: Optional[AwardsSection] = Field(default_factory=lambda: AwardsSection(), description="Awards section")
    references: Optional[ReferencesSection] = Field(default_factory=lambda: ReferencesSection(), description="References section")
    profile: Optional[ProfileSection] = Field(default_factory=lambda: ProfileSection(), description="Profile section")
    custom: Optional[dict] = Field(default={}, description="Custom section")

class CssMetadata(BaseModel):
    value: str = Field(default=".section {outline:1px solid #000;outline-offset:4px;}", description="Custom CSS value")
    visible: bool = Field(default=False, description="Whether CSS is visible")

class PageOptions(BaseModel):
    breakLine: bool = Field(default=True, description="Whether to break line")
    pageNumbers: bool = Field(default=True, description="Whether to show page numbers")

class PageMetadata(BaseModel):
    format: str = Field(default="a4", description="Page format")
    margin: int = Field(default=14, description="Page margin")
    options: PageOptions = Field(default_factory=lambda: PageOptions(), description="Page options")

class ThemeMetadata(BaseModel):
    text: str = Field(default="#000000", description="Text color")
    primary: str = Field(default="#ca8a04", description="Primary color")
    background: str = Field(default="#ffffff", description="Background color")

class FontMetadata(BaseModel):
    size: int = Field(default=13, description="Font size")
    family: str = Field(default="Merriweather", description="Font family")
    subset: str = Field(default="latin", description="Font subset")
    variants: List[str] = Field(default=["regular"], description="Font variants")

class TypographyMetadata(BaseModel):
    font: FontMetadata = Field(default_factory=lambda: FontMetadata(), description="Font metadata")
    hideIcons: bool = Field(default=False, description="Whether to hide icons")
    lineHeight: float = Field(default=1.75, description="Line height")
    underlineLinks: bool = Field(default=True, description="Whether to underline links")

class Metadata(BaseModel):
    css: CssMetadata = Field(default_factory=lambda: CssMetadata(), description="CSS metadata")
    page: PageMetadata = Field(default_factory=lambda: PageMetadata(), description="Page metadata")
    notes: str = Field(default="NextCareer", description="Additional notes")
    theme: ThemeMetadata = Field(default_factory=lambda: ThemeMetadata(), description="Theme metadata")
    layout: List[List[List[str]]] = Field(
        default=[[["summary", "experience", "education", "publications", "projects", "volunteer", "references"],
                 ["profiles", "skills", "certifications", "interests", "languages", "awards"]]],
        description="Layout configuration for resume sections"
    )
    template: str = Field(default="glalie", description="Resume template name")
    typography: TypographyMetadata = Field(default_factory=lambda: TypographyMetadata(), description="Typography metadata")

class ResumeSchema(BaseModel):
    basics: Optional[Basics] = Field(default_factory=lambda: Basics(), description="Basic resume information")
    sections: Optional[Sections] = Field(default_factory=lambda: Sections(), description="Resume sections")
    metadata: Optional[Metadata] = Field(default_factory=lambda: Metadata(), description="Resume metadata")

class ResumeResponse(BaseModel):
    error: int = Field(default=0, description="Error code, 0 means no error")
    message: str = Field(default="Success", description="Response message")
    data: ResumeSchema = Field(default_factory=lambda: ResumeSchema(), description="Resume data")




class CFG:
    prompt_basic_info = \
"""
Action: Extract the resume info below into only a JSON (Expecting property name enclosed in double quotes) with exactly the following format (If any fields cannot be found in the resume, please fill them with "N/A" instead of blank string "" or 0). Return a response with json format.
Context: A resume from a person work in Health Care domain.
There are possible abbreviations in the resume such as:
- RN: Registered Nurse
- BSN: Bachelor of Science in Nursing
- MSN: Master of Science in Nursing
- NP: Nurse Practitioner
- DNP: Doctor of Nursing Practice
- APRN: Advanced Practice Registered Nurse
- CRNA: Certified Registered Nurse Anesthetist
- CNM: Certified Nurse Midwife
- CNS: Clinical Nurse Specialist
- CNA: Certified Nursing Assistant
- LPN: Licensed Practical Nurse
- LVN: Licensed Vocational Nurse
- CMA: Certified Medical Assistant
- CNA: Certified Nursing Assistant
- CMA: Certified Medical Assistant
- BLS: Basic Life Support
- ACLS: Advanced Cardiac Life Support
- CVICU: Cardiovascular Intensive Care Unit
- SICU: Surgical Intensive Care Unit
- MICU: Medical Intensive Care Unit
- EHR: Electronic Health Record
- NREMT: National Registry of Emergency Medical Technicians
-----------
Json format:
{
"basic_information":
{"first_name",
"last_name",
"full_name",
"email",
"phone_number" (format: (XXX)-XXX-XXXX),
"has_compact_license" (possible values: "Yes", "No", "No Information"),
"date_of_birth" (format: yyyy-MM-dd)
},
"worker_address":
{"street",
"city",
"state" (2 uppercase characters),
"zipcode"}
}
-----------
TEXT:
"""

    prompt_relation_info = \
"""
Action: Extract the resume info (contain information of worker in healthcare domain) below into only a JSON (Expecting property name enclosed in double quotes) with exactly the following format (If any fields cannot be found in the resume, please fill them with "N/A" instead of blank string "" or 0).Return a response with json format.
Context: A resume from a person work in Health Care domain.
There are possible abbreviations in the resume such as:
- RN: Registered Nurse
- BSN: Bachelor of Science in Nursing
- MSN: Master of Science in Nursing
- NP: Nurse Practitioner
- DNP: Doctor of Nursing Practice
- APRN: Advanced Practice Registered Nurse
- CRNA: Certified Registered Nurse Anesthetist
- CNM: Certified Nurse Midwife
- CNS: Clinical Nurse Specialist
- CNA: Certified Nursing Assistant
- LPN: Licensed Practical Nurse
- LVN: Licensed Vocational Nurse
- CMA: Certified Medical Assistant
- CNA: Certified Nursing Assistant
- CMA: Certified Medical Assistant
- BLS: Basic Life Support
- ACLS: Advanced Cardiac Life Support
- CVICU: Cardiovascular Intensive Care Unit
- SICU: Surgical Intensive Care Unit
- MICU: Medical Intensive Care Unit
- EHR: Electronic Health Record
- NREMT: National Registry of Emergency Medical Technicians
-----------
Json format:
{
"work_experiences" (array of objects):
[
{
"title",
"facility_name",
"facility_state" (2 uppercase characters),
"country",
"facility_city",
"currently_employed" (boolean) (must be values: "yes","no"),
"start_year" (return null if cannot find in RESUME),
"start_month" (return null if cannot find in RESUME),
"start_day" (return null if cannot find in RESUME),
"end_year" (return null if cannot find in RESUME),
"end_month" (return null if cannot find in RESUME),
"end_day" (return null if cannot find in RESUME),
}
],
"worker_educations" (array of objects):
[
{
"major",
"school_name",
"school_state" (2 uppercase characters),
"school_country",
"degree_name",
"start_year" (return null if cannot find in RESUME),
"start_month" (return null if cannot find in RESUME),
"start_day" (return null if cannot find in RESUME),
"end_year" (return null if cannot find in RESUME),
"end_month" (return null if cannot find in RESUME),
"end_day" (return null if cannot find in RESUME)
}
],
"worker_references" (array of objects):
[
{
"facility_name",
"contact_email",
"contact_phone" (format: (XXX)-XXX-XXXX),
"contact_first_name",
"contact_last_name",
"job_title",
}
],
"certifications" (array of objects):
[
{
"title",
"expire_year" (return null if cannot find in RESUME),
"expire_month" (return null if cannot find in RESUME),
"expire_day" (return null if cannot find in RESUME)
}
]
}
-----------
RESUME:
"""

    new_prompt_basic_info = """
    You are an expert resume parser that extracts comprehensive information from resumes. Your primary goal is to PRESERVE ALL ORIGINAL CONTENT exactly as written while organizing it into a structured JSON format.

    CRITICAL INSTRUCTIONS:
    1. PRESERVE EXACT ORIGINAL TEXT - Do not paraphrase, summarize, or modify any content from the resume
    2. EXTRACT COMPLETE CONTENT - Include every word, sentence, and detail from each section
    3. MAINTAIN ORIGINAL FORMATTING - Keep bullet points, line breaks, and structure where relevant
    4. USE ORIGINAL WORDING - Copy text exactly as it appears in the resume
    5. INCLUDE ALL DETAILS - Do not omit any information, achievements, or descriptions

    Return the information in the following JSON format:

    {
      "basics": {
        "fullname": "Extract the complete full name exactly as written",
        "headline": "Extract the exact professional headline/title as written (if available)",
        "email": "Extract email address exactly as formatted",
        "phone": "Extract phone number in original format",
        "location": "Extract complete address/location exactly as written",
        "url": {
          "label": "Extract website label exactly as written (default: empty string)",
          "href": "Extract complete URL exactly as written, ensure it starts with https:// (default: empty string)"
        },
        "customFields": [
          {
            "id": "custom_field_1",
            "icon": "Appropriate icon name for the field type",
            "name": "Extract field name exactly as written",
            "value": "Extract field value exactly as written"
          }
        ],
        "picture": {
          "url": "Default: https://i.imgur.com/HgwyOuJ.jpg",
          "size": 64,
          "aspectRatio": 1,
          "borderRadius": 0,
          "effects": {
            "hidden": false,
            "border": false,
            "grayscale": false
          }
        }
      },
      "metadata": {
        "template": "gengar",
        "layout": [
          [
            [
              ["profiles"],
              ["summary"],
              ["experience"],
              ["volunteer"],
              ["references"],
              ["projects"]
            ],
            [
              ["skills"],
              ["education"],
              ["interests"],
              ["certifications"],
              ["awards"],
              ["publications"],
              ["languages"]
            ]
          ]
        ],
        "css": {
          "value": ".section {outline:1px solid #000;outline-offset:4px;}",
          "visible": false
        },
        "page": {
          "margin": 12,
          "format": "letter",
          "options": {
            "breakLine": true,
            "pageNumbers": true
          }
        },
        "theme": {
          "background": "#ffffff",
          "text": "#000000",
          "primary": "#57534e"
        },
        "typography": {
          "font": {
            "family": "Lora",
            "subset": "latin",
            "variants": ["regular"],
            "size": 12
          },
          "lineHeight": 1.45,
          "hideIcons": false,
          "underlineLinks": false
        },
        "notes": ""
      },
      "sections": {
        "summary": {
          "name": "Summary",
          "columns": 1,
          "separateLinks": true,
          "visible": true,
          "id": "summary",
          "content": "EXTRACT THE COMPLETE SUMMARY/OBJECTIVE/PROFILE SECTION EXACTLY AS WRITTEN. Include every sentence, phrase, and detail. Preserve all original wording, achievements, and qualifications mentioned. Do not summarize or paraphrase - copy the entire content verbatim. Empty string if not mentioned."
        },
        "profiles": {
          "name": "Profiles",
          "id": "profiles",
          "columns": 1,
          "separateLinks": true,
          "visible": true,
          "items": [
            {
              "id": "profile_1",
              "name": "Extract platform name exactly as written",
              "visible": true,
              "network": "Extract network name (linkedin, github, etc.)",
              "icon": "Corresponding icon name",
              "username": "Extract username or handle exactly as written (e.g., @username, username, or profile handle)",
              "url": {
                "label": "Extract label exactly as written",
                "href": "Extract complete URL exactly as written, ensure https:// prefix"
              }
            }
          ]
        },
        "skills": {
          "name": "Skills",
          "columns": 1,
          "separateLinks": true,
          "visible": true,
          "id": "skills",
          "items": [
            {
              "id": "skill_1",
              "name": "Extract skill category exactly as written (e.g., 'Programming Languages', 'Technical Skills')",
              "description": "Extract proficiency level exactly as written (if mentioned). Empty string if not mentioned.",
              "keywords": ["Extract each individual skill exactly as written", "Include ALL skills listed", "Preserve original terminology"],
              "level": 0,
              "visible": true
            }
          ]
        },
        "experience": {
          "name": "Experience",
          "columns": 1,
          "separateLinks": true,
          "visible": true,
          "id": "experience",
          "items": [
            {
              "id": "experience_1",
              "company": "Extract company name exactly as written",
              "position": "Extract job title exactly as written",
              "location": "Extract location exactly as written",
              "date": "Extract employment period exactly as formatted",
              "summary": "EXTRACT THE COMPLETE JOB DESCRIPTION EXACTLY AS WRITTEN. Include every responsibility, achievement, bullet point, and detail. Preserve all metrics, technologies, and accomplishments mentioned. Do not summarize - copy the entire content verbatim. Empty string if not mentioned.",
              "url": {
                "label": "Extract company website label if mentioned",
                "href": "Extract company URL if mentioned, ensure https:// prefix"
              },
              "visible": true
            }
          ]
        },
        "education": {
          "name": "Education",
          "columns": 1,
          "separateLinks": true,
          "visible": true,
          "id": "education",
          "items": [
            {
              "id": "education_1",
              "institution": "Extract school/university name exactly as written",
              "studyType": "Extract degree type exactly as written (Bachelor's, Master's, etc.)",
              "visible": true,
              "score": "Extract GPA/score exactly as written (if mentioned)",
              "area": "Extract field of study exactly as written",
              "date": "Extract education period exactly as formatted",
              "summary": "EXTRACT ALL EDUCATION DETAILS EXACTLY AS WRITTEN. Include honors, relevant coursework, projects, achievements, and any additional details mentioned. Preserve all original content. Empty string if not mentioned.",
              "url": {
                "label": "Extract institution website label if mentioned",
                "href": "Extract institution URL if mentioned, ensure https:// prefix"
              }
            }
          ]
        },
        "certifications": {
          "name": "Certifications",
          "columns": 1,
          "separateLinks": true,
          "visible": true,
          "id": "certifications",
          "items": [
            {
              "id": "certification_1",
              "name": "Extract certification name exactly as written",
              "issuer": "Extract issuing organization exactly as written",
              "date": "Extract date obtained exactly as formatted",
              "visible": true,
              "summary": "EXTRACT ALL CERTIFICATION DETAILS EXACTLY AS WRITTEN. Include any descriptions, validity periods, or additional information mentioned. Empty string if not mentioned.",
              "url": {
                "label": "Extract certification URL label if mentioned",
                "href": "Extract certification URL if mentioned, ensure https:// prefix"
              }
            }
          ]
        },
        "projects": {
          "name": "Projects",
          "columns": 1,
          "separateLinks": true,
          "visible": true,
          "id": "projects",
          "items": [
            {
              "id": "project_1",
              "name": "Extract project name exactly as written",
              "visible": true,
              "description": "Extract project type/role exactly as written",
              "summary": "EXTRACT THE COMPLETE PROJECT DESCRIPTION EXACTLY AS WRITTEN. Include every detail about technologies used, achievements, outcomes, and methodologies. Preserve all technical terms and metrics mentioned. Empty string if not mentioned.",
              "date": "Extract project period exactly as formatted",
              "keywords": ["Extract all technologies mentioned", "Include all tools and frameworks", "Preserve technical terminology"],
              "url": {
                "label": "Extract project URL label if mentioned",
                "href": "Extract project URL if mentioned, ensure https:// prefix"
              }
            }
          ]
        },
        "publications": {
          "name": "Publications",
          "columns": 1,
          "separateLinks": true,
          "visible": true,
          "id": "publications",
          "items": [
            {
              "id": "publication_1",
              "visible": true,
              "name": "Extract publication title exactly as written",
              "publisher": "Extract publisher name exactly as written",
              "date": "Extract publication date exactly as formatted",
              "summary": "EXTRACT ALL PUBLICATION DETAILS EXACTLY AS WRITTEN. Include abstracts, co-authors, and any additional information mentioned. Empty string if not mentioned.",
              "url": {
                "label": "Extract publication URL label if mentioned",
                "href": "Extract publication URL if mentioned, ensure https:// prefix"
              }
            }
          ]
        },
        "volunteer": {
          "name": "Volunteer",
          "columns": 1,
          "separateLinks": true,
          "visible": true,
          "id": "volunteer",
          "items": [
            {
              "id": "volunteer_1",
              "visible": true,
              "organization": "Extract organization name exactly as written, N/A if not mentioned",
              "position": "Extract volunteer position exactly as written",
              "date": "Extract volunteer period exactly as formatted",
              "summary": "EXTRACT THE COMPLETE VOLUNTEER DESCRIPTION EXACTLY AS WRITTEN. Include all responsibilities, achievements, and impact mentioned. Empty string if not mentioned.",
              "location": "Extract location exactly as written, N/A if not mentioned",
              "url": {
                "label": "Extract organization URL label if mentioned",
                "href": "Extract organization URL if mentioned, ensure https:// prefix"
              }
            }
          ]
        },
        "languages": {
          "name": "Languages",
          "columns": 1,
          "separateLinks": true,
          "visible": true,
          "id": "languages",
          "items": [
            {
              "id": "language_1",
              "name": "Extract language name exactly as written",
              "description": "Extract proficiency level exactly as written",
              "visible": true
            }
          ]
        },
        "interests": {
          "name": "Interests",
          "columns": 1,
          "separateLinks": true,
          "visible": true,
          "id": "interests",
          "items": [
            {
              "id": "interest_1",
              "name": "Extract interest category exactly as written",
              "visible": true,
              "keywords": ["Extract each interest exactly as written", "Include all hobbies and interests mentioned"]
            }
          ]
        },
        "awards": {
          "name": "Awards",
          "columns": 1,
          "separateLinks": true,
          "visible": true,
          "id": "awards",
          "items": [
            {
              "id": "award_1",
              "title": "Extract award name exactly as written. If not mentioned, return 'N/A'",
              "awarder": "Extract awarding organization exactly as written",
              "date": "Extract date received exactly as formatted",
              "summary": "EXTRACT ALL AWARD DETAILS EXACTLY AS WRITTEN. Include criteria, significance, and any additional information mentioned. Empty string if not mentioned.",
              "visible": true,
              "url": {
                "label": "Extract award URL label if mentioned",
                "href": "Extract award URL if mentioned, ensure https:// prefix"
              }
            }
          ]
        },
        "references": {
          "name": "References",
          "columns": 1,
          "separateLinks": true,
          "visible": true,
          "id": "references",
          "items": [
            {
              "id": "reference_1",
              "name": "Extract reference name exactly as written",
              "visible": true,
              "description": "Extract reference title/relationship exactly as written",
              "summary": "EXTRACT ALL REFERENCE DETAILS EXACTLY AS WRITTEN. Include contact information, relationship, emails, phone numbers, and any testimonials mentioned. Empty string if not mentioned.",
              "url": {
                "label": "Extract reference URL label if mentioned",
                "href": "Extract reference URL if mentioned, ensure https:// prefix"
              }
            }
          ]
        },
        "custom": {}
      }
    }

    EXTRACTION RULES:
    1. **PRESERVE ORIGINAL CONTENT**: Copy text exactly as it appears in the resume
    2. **COMPLETE EXTRACTION**: Include every piece of information available
    3. **NO PARAPHRASING**: Use the exact words and phrases from the resume
    4. **MAINTAIN STRUCTURE**: Preserve bullet points and formatting in text fields
    5. **INCLUDE ALL DETAILS**: Don't omit any achievements, metrics, or technical details
    6. **EXACT TERMINOLOGY**: Keep all industry-specific terms and jargon
    7. **FULL DESCRIPTIONS**: Include complete job descriptions and project details
    8. **ALL ACHIEVEMENTS**: Include every accomplishment and result mentioned
    9. **TECHNICAL ACCURACY**: Preserve all technical terms, tools, and technologies
    10. **COMPREHENSIVE COVERAGE**: Ensure no section or detail is overlooked

    If any section is not present in the resume, omit that section from the JSON response entirely rather than including empty defaults.

    Remember: Your goal is to create a complete digital representation of the resume while preserving every word and detail exactly as the original author wrote it.
    """


