import re
from typing import Dict, List, Tuple
from bs4 import <PERSON><PERSON>oup
from collections import Counter
import json
import os
from groq import Groq
from sanic.log import logger
from utils import llm_helpers

def extract_text_from_sections(sections: Dict) -> str:
    """Extract plain text from all sections in the resume, stripping HTML tags."""
    text = ""
    for section_data in sections.values():
        if "content" in section_data:
            soup = BeautifulSoup(section_data["content"], "html.parser")
            text += soup.get_text() + " "
        elif "items" in section_data:
            for item in section_data["items"]:
                if "summary" in item:
                    soup = BeautifulSoup(item["summary"], "html.parser")
                    text += soup.get_text() + " "
    return text.strip()

def count_bullets(sections: Dict) -> int:
    """Count the total number of bullet points (<li> tags) in the resume."""
    bullet_count = 0
    for section_data in sections.values():
        if "items" in section_data:
            for item in section_data["items"]:
                if "summary" in item:
                    soup = BeautifulSoup(item["summary"], "html.parser")
                    bullet_count += len(soup.find_all("li"))
    return bullet_count

def count_sections_with_bullets(sections: Dict) -> int:
    """Count the number of sections that contain at least one bullet point."""
    sections_with_bullets = 0
    for section_data in sections.values():
        if "items" in section_data:
            has_bullets = any(
                BeautifulSoup(item["summary"], "html.parser").find("li") is not None
                for item in section_data["items"] if "summary" in item
            )
            if has_bullets:
                sections_with_bullets += 1
    return sections_with_bullets

def get_bullets_per_section(sections: Dict) -> Dict[str, int]:
    """Count the total number of bullets per section."""
    bullets_per_section = {}
    for section_name, section_data in sections.items():
        if "items" in section_data:
            total_bullets = 0
            for item in section_data["items"]:
                if "summary" in item:
                    soup = BeautifulSoup(item["summary"], "html.parser")
                    total_bullets += len(soup.find_all("li"))
            if total_bullets > 0:
                bullets_per_section[section_name] = total_bullets
    return bullets_per_section

def count_bolds_and_length(sections: Dict) -> Tuple[int, int]:
    """Count the number of bold tags and total characters in bolded text."""
    num_bolds = 0
    length_bold_text_chars = 0
    for section_data in sections.values():
        if "content" in section_data:
            soup = BeautifulSoup(section_data["content"], "html.parser")
            bolds = soup.find_all(["strong", "b"])
            num_bolds += len(bolds)
            length_bold_text_chars += sum(len(bold.get_text()) for bold in bolds)
        elif "items" in section_data:
            for item in section_data["items"]:
                if "summary" in item:
                    soup = BeautifulSoup(item["summary"], "html.parser")
                    bolds = soup.find_all(["strong", "b"])
                    num_bolds += len(bolds)
                    length_bold_text_chars += sum(len(bold.get_text()) for bold in bolds)
    return num_bolds, length_bold_text_chars

def find_phrases(text: str, phrases: List[str]) -> List[str]:
    """Find all occurrences of specified phrases in the text, case-insensitive."""
    found = []
    for phrase in phrases:
        matches = re.findall(r'\b' + re.escape(phrase) + r'\b', text, re.IGNORECASE)
        found.extend(matches)
    return found

def calculate_ats_score(schema):
    # Extract features from the schema
    features = schema["features_and_ratings"]["features"]
    
    # 1. Keyword Matching Score
    # Directly use matching_jd_resume as the keyword score (assumed to be a proportion between 0 and 1)
    keyword_score = features["matching_jd_resume"]
    
    # 2. Structure Score
    # List of essential fields that ATS systems typically require
    essential_fields = [
        "has_work", "has_education", "has_skills", "has_summary",
        "has_email", "has_phone", "has_first_name", "has_last_name"
    ]
    # Calculate the proportion of essential fields present (True values)
    structure_score = sum(features.get(field, False) for field in essential_fields) / len(essential_fields)
    
    # 3. Content Quality Score
    # Extract relevant fields
    passive_voice = features["passive_voice"]
    num_words = features["num_words"]
    unique_percentage = features["unique_percentage"]
    language_prob = features["professional_score"]
    
    # Passive Voice Score: 1 if <10% of words, else decrease linearly, minimum 0
    if num_words > 0:
        passive_voice_ratio = passive_voice / num_words
    else:
        passive_voice_ratio = 0
    passive_voice_score = 1 if passive_voice_ratio < 0.1 else max(0, 1 - 10 * (passive_voice_ratio - 0.1))
    
    # Word Count Score: Ideal range is 400-600 words
    if num_words <= 400:
        word_count_score = num_words / 400  # Scale up to 400
    elif num_words <= 600:
        word_count_score = 1  # Perfect range
    else:
        word_count_score = 600 / num_words  # Scale down if too long
    
    # Unique Percentage Score: Directly use the proportion of unique words
    unique_percentage_score = unique_percentage
    
    # Language Score: Confidence that the resume is in a parsable language (e.g., English)
    language_score = language_prob
    
    # Average the content quality sub-scores
    content_quality_score = (
        passive_voice_score + word_count_score + unique_percentage_score + language_score
    ) / 4
    
    # Calculate final ATS score with weights: 50% keyword, 25% structure, 25% content quality
    ats_score = 0.25 * keyword_score + 0.5 * structure_score + 0.25 * content_quality_score
    
    return int(ats_score*100), keyword_score, structure_score, content_quality_score

def estimate_tokens(text: str) -> int:
    """Estimate the number of tokens in text using a simple approximation."""
    # Rough estimate: 1 token ≈ 4 characters for English text
    return len(text) // 4

def chunk_text(text: str, max_tokens: int = 2500) -> List[str]:
    """Split text into chunks that don't exceed max_tokens."""
    if estimate_tokens(text) <= max_tokens:
        return [text]
    
    # Split by sentences first to maintain context
    sentences = re.split(r'[.!?]+', text)
    chunks = []
    current_chunk = ""
    
    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue
            
        # Check if adding this sentence would exceed the limit
        potential_chunk = current_chunk + " " + sentence if current_chunk else sentence
        if estimate_tokens(potential_chunk) > max_tokens:
            if current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = sentence
            else:
                # Single sentence is too long, split by words
                words = sentence.split()
                word_chunk = ""
                for word in words:
                    potential_word_chunk = word_chunk + " " + word if word_chunk else word
                    if estimate_tokens(potential_word_chunk) > max_tokens:
                        if word_chunk:
                            chunks.append(word_chunk.strip())
                        word_chunk = word
                    else:
                        word_chunk = potential_word_chunk
                if word_chunk:
                    current_chunk = word_chunk
        else:
            current_chunk = potential_chunk
    
    if current_chunk:
        chunks.append(current_chunk.strip())
    
    return chunks

def average_analysis_results(results: List[Dict]) -> Dict:
    """Average the analysis results from multiple chunks."""
    if not results:
        return {}
    
    if len(results) == 1:
        return results[0]
    
    # Initialize averaged result
    averaged = {}
    
    # Handle jd_keywords - combine and deduplicate
    all_keywords = []
    for result in results:
        all_keywords.extend(result.get("jd_keywords", []))
    averaged["jd_keywords"] = list(set(all_keywords))  # Remove duplicates
    
    # Handle numeric values - take average
    numeric_fields = ["passive_voice_count", "professional_score"]
    for field in numeric_fields:
        values = [result.get(field, 0) for result in results if field in result]
        averaged[field] = sum(values) / len(values) if values else 0
    
    # Handle bullet_analysis - average the metrics
    bullet_analyses = [result.get("bullet_analysis", {}) for result in results]
    if bullet_analyses:
        all_bullets = []
        all_lengths = []
        for ba in bullet_analyses:
            if "all_bullets" in ba:
                all_bullets.extend(ba["all_bullets"])
        
        for bullet in all_bullets:
            all_lengths.append(len(bullet))
        
        if all_lengths:
            averaged["bullet_analysis"] = {
                "max_length": max(all_lengths),
                "min_length": min(all_lengths),
                "avg_length": sum(all_lengths) / len(all_lengths),
                "all_bullets": all_bullets
            }
        else:
            averaged["bullet_analysis"] = {"max_length": 182, "min_length": 57, "avg_length": 133.33}
    
    return averaged

def average_quality_results(results: List[Dict]) -> Dict:
    """Average the quality analysis results from multiple chunks."""
    if not results:
        return {"quality_score": 0, "missing_keywords": []}
    
    if len(results) == 1:
        return results[0]
    
    # Average quality scores
    quality_scores = [result.get("quality_score", 0) for result in results]
    avg_quality_score = sum(quality_scores) / len(quality_scores) if quality_scores else 0
    
    # Combine missing keywords and deduplicate
    all_missing = []
    for result in results:
        all_missing.extend(result.get("missing_keywords", []))
    unique_missing = list(set(all_missing))
    
    return {
        "quality_score": avg_quality_score,
        "missing_keywords": unique_missing[:5]  # Limit to top 5
    }

def average_synonym_results(results: List[Dict]) -> Dict:
    """Average the synonym analysis results from multiple chunks."""
    if not results:
        return {}
    
    if len(results) == 1:
        return results[0]
    
    # Combine repeated words from all chunks
    combined_repeated_words = {}
    
    for result in results:
        repeated_words = result.get("repeated_words", {})
        for word, data in repeated_words.items():
            if word in combined_repeated_words:
                # Merge data for the same word
                existing = combined_repeated_words[word]
                existing["usages"].extend(data.get("usages", []))
                existing["usage_count"] += data.get("usage_count", 0)
                existing["usages_as_verbs"].extend(data.get("usages_as_verbs", []))
            else:
                combined_repeated_words[word] = data.copy()
    
    return {"repeated_words": combined_repeated_words}

def robust_json_parse(text: str) -> dict:
    """Parse JSON with robust error handling and cleanup."""
    # Clean up the response text
    text = text.strip()
    
    # Remove code block markers
    if "```json" in text:
        text = text.split("```json")[1].split("```")[0].strip()
    elif "```" in text:
        text = text.split("```")[1].split("```")[0].strip()
    
    # Try to find JSON object between curly braces
    import re
    json_match = re.search(r'\{.*\}', text, re.DOTALL)
    if json_match:
        text = json_match.group()
    
    # Clean up common formatting issues
    text = re.sub(r',\s*}', '}', text)  # Remove trailing commas
    text = re.sub(r',\s*]', ']', text)  # Remove trailing commas in arrays
    
    try:
        return json.loads(text)
    except json.JSONDecodeError as e:
        logger.error(f"JSON parse error: {e}")
        logger.error(f"Problematic text: {text[:300]}...")
        # Return empty dict as fallback
        return {}

def analyze_resume(resume_json: Dict, job_description: str, is_free_user: bool) -> Dict:
    """Analyze the resume JSON and return features according to the specified schema."""
    sections = resume_json.get("sections", {})
    basics = resume_json.get("basics", {})

    # Extract and process text
    text = extract_text_from_sections(sections)
    # words = word_tokenize(text)
    words = re.findall(r'\b[a-zA-Z]+\b', text)
    words_lower = [w.lower() for w in words]

    stop_words = ["are", "only", "ourselves", "have", "don", "myself", "weren't", "you", "some", "they're", "you're", "itself", "should", "whom", "herself", "now", "any", "our", "that", "of", "d", "not", "we", "until", "while", "same", "for", "y", "again", "before", "that'll", "than", "you'd", "through", "do", "s", "because", "during", "wasn't", "each", "m", "weren", "mightn't", "down", "couldn't", "i've", "i'd", "his", "haven", "needn", "after", "they'll", "this", "we'd", "my", "ma", "hasn't", "against", "didn't", "re", "yours", "shan", "and", "doing", "ain", "ll", "theirs", "o", "been", "between", "himself", "she'd", "mightn", "themselves", "did", "hasn", "if", "both", "i", "who", "shan't", "wouldn't", "was", "to", "the", "them", "me", "mustn", "hadn", "does", "over", "at", "when", "your", "we've", "being", "by", "couldn", "yourselves", "they've", "into", "i'll", "few", "is", "needn't", "won't", "further", "but", "most", "she", "just", "those", "then", "mustn't", "having", "how", "her", "ours", "nor", "wasn", "up", "which", "what", "doesn't", "yourself", "in", "he'd", "it's", "more", "or", "shouldn't", "aren", "its", "were", "should've", "didn", "am", "you've", "he", "isn't", "own", "be", "she'll", "such", "all", "from", "has", "off", "haven't", "there", "with", "too", "as", "aren't", "he's", "out", "hers", "you'll", "it", "won", "their", "they", "above", "it'd", "doesn", "on", "wouldn", "him", "a", "so", "isn", "can", "other", "we'll", "she's", "about", "these", "here", "hadn't", "had", "they'd", "below", "he'll", "t", "where", "will", "no", "very", "why", "ve", "an", "once", "shouldn", "under", "don't", "it'll", "we're", "i'm"]
    # stop_words = set(stopwords.words('english'))

    # Basic text metrics
    num_words = len(words)
    num_characters = len(text)
    num_stop_words = sum(1 for w in words_lower if w in stop_words)
    unique_words = set(words_lower)
    num_unique_words = len(unique_words)
    non_stop_words = [w for w in words_lower if w not in stop_words]
    num_unique_non_stop_words = len(set(non_stop_words))
    unique_percentage = num_unique_words / num_words if num_words else 0
    unique_non_stop_percentage = num_unique_non_stop_words / len(non_stop_words) if non_stop_words else 0

    # Extract keywords from job description using Groq
    MODEL = llm_helpers.get_available_groq_model()
    client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))
    job_description = job_description or ""
    
    # Check if we need to chunk the text
    combined_text = text + " " + job_description
    estimated_tokens = estimate_tokens(combined_text)
    logger.info(f"Estimated tokens for resume analysis: {estimated_tokens}")
    
    if estimated_tokens > 100000:
        logger.info("Text exceeds 100000 tokens, chunking into smaller pieces")
        text_chunks = chunk_text(text, max_tokens=990000)  # Leave room for job description and prompt
        logger.info(f"Split into {len(text_chunks)} chunks")
    else:
        text_chunks = [text]
    
    # Process each chunk
    chunk_results = []
    for i, chunk in enumerate(text_chunks):
        logger.info(f"Processing chunk {i+1}/{len(text_chunks)}")
        
        # Rewritten prompt to ensure valid JSON output and avoid model confusion with types/formatting.
        if job_description:
            prompt = f"""
You are an expert resume analyzer. Please analyze the following resume text and job description, and return your results as a single valid JSON object. 
Strictly follow the output schema and types below. Do not include any explanations or extra text.

Schema:
{{
  "jd_keywords": [string, ...],  // Array of up to 20 most relevant skills, technologies, and qualifications from the job description.
  "passive_voice_count": integer,  // Number of passive voice constructions in the resume text.
  "professional_score": float,     // Professional quality of the resume text, between 0 and 1.
  "bullet_analysis": {{
    "max_length": integer,         // Maximum bullet length in characters.
    "min_length": integer,         // Minimum bullet length in characters.
    "avg_length": float,           // Average bullet length in characters.
    "all_bullets": [integer, ...]  // Array of bullet point lengths in characters.
  }}
}}

Instructions:
1. From the job description, extract up to 20 of the most important keywords (skills, technologies, qualifications) as an array of strings.
2. In the resume text, count the number of passive voice constructions.
3. Rate the professional quality of the resume text on a scale from 0 (unprofessional, vague, cliché) to 1 (highly professional, specific, achievement-oriented).
4. Extract all bullet points from the resume text. For each bullet, calculate its length in characters. Then, provide:
   - The maximum bullet length (max_length)
   - The minimum bullet length (min_length)
   - The average bullet length (avg_length, as a float)
   - An array of all bullet lengths (all_bullets)

Resume Text:
\"\"\"{chunk}\"\"\"

Job Description:
\"\"\"{job_description}\"\"\"

Return only the JSON object as specified above.
"""
        else:
            prompt = f"""
You are an expert resume analyzer. Please analyze the following resume text and return your results as a single valid JSON object.
Strictly follow the output schema and types below. Do not include any explanations or extra text.

Schema:
{{
  "passive_voice_count": integer,  // Number of passive voice constructions in the resume text.
  "professional_score": float,     // Professional quality of the resume text, between 0 and 1.
  "bullet_analysis": {{
    "max_length": integer,         // Maximum bullet length in characters.
    "min_length": integer,         // Minimum bullet length in characters.
    "avg_length": float,           // Average bullet length in characters.
    "all_bullets": [integer, ...]  // Array of bullet point lengths in characters.
  }}
}}

Instructions:
1. In the resume text, count the number of passive voice constructions.
2. Rate the professional quality of the resume text on a scale from 0 (unprofessional, vague, cliché) to 1 (highly professional, specific, achievement-oriented).
3. Extract all bullet points from the resume text. For each bullet, calculate its length in characters. Then, provide:
   - The maximum bullet length (max_length)
   - The minimum bullet length (min_length)
   - The average bullet length (avg_length, as a float)
   - An array of all bullet lengths (all_bullets)

Resume Text:
\"\"\"{chunk}\"\"\"

Return only the JSON object as specified above.
"""
        
        try:
            response = client_groq.chat.completions.create(
                model=MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert resume analyzer. Extract key information from resumes and job descriptions into structured data."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,
                response_format={"type": "json_object"},
                seed=12
            )
            
            # Extract analysis from the response
            analysis_text = response.choices[0].message.content
            # Clean up the response to get just the JSON part
            analysis_text = analysis_text.strip()
            if "```json" in analysis_text:
                analysis_text = analysis_text.split("```json")[1].split("```")[0].strip()
            elif "```" in analysis_text:
                analysis_text = analysis_text.split("```")[1].strip()
            
            analysis_data = robust_json_parse(analysis_text)
            chunk_results.append(analysis_data)
            
        except Exception as e:
            logger.error(f"Error processing chunk {i+1}: {e}")
            # Add fallback result for this chunk
            fallback_result = {
                "jd_keywords": [],
                "passive_voice_count": 0,
                "professional_score": 0,
                "bullet_analysis": {"max_length": 1, "min_length": 9, "avg_length": 99, "all_bullets": []}
            }
            chunk_results.append(fallback_result)
    
    # Average the results from all chunks
    if chunk_results:
        averaged_analysis = average_analysis_results(chunk_results)
        jd_keywords = averaged_analysis.get("jd_keywords", [])
        passive_voice = averaged_analysis.get("passive_voice_count", 0)
        professional_score = averaged_analysis.get("professional_score", 0)
        
        # Extract bullet analysis
        bullet_analysis = averaged_analysis.get("bullet_analysis", {})
        max_bullet_length_chars = bullet_analysis.get("max_length", 182)
        min_bullet_length_chars = bullet_analysis.get("min_length", 57)
        average_bullet_length_chars = bullet_analysis.get("avg_length", 133.33333333333334)
    else:
        # Fallback if no chunks processed successfully
        jd_words = re.findall(r'\b[a-zA-Z]+\b', job_description.lower())
        jd_words_filtered = [w for w in jd_words if w.isalnum() and w not in stop_words and len(w) > 2]
        jd_word_freq = Counter(jd_words_filtered)
        jd_keywords = [word for word, count in jd_word_freq.most_common(20)]
        passive_voice = 0
        professional_score = 0
        max_bullet_length_chars = 1
        min_bullet_length_chars = 9
        average_bullet_length_chars = 99
    
    # Check for keyword matches in resume
    resume_text_lower = text.lower()
    matching_keywords = []
    missing_keywords = []
    
    if jd_keywords:
        for keyword in jd_keywords:
            # Check if the keyword exists in the resume text
            if keyword.lower() in resume_text_lower:
                matching_keywords.append(keyword)
            else:
                missing_keywords.append(keyword)
        # Calculate matching score (0-1)
        # matching_jd_resume = len(matching_keywords) / len(jd_keywords) if jd_keywords else 0
    else:
        matching_keywords, missing_keywords = [], []
    # Evaluate the quality of resume keywords using Groq
    keyword_quality_prompt = f"""
    Evaluate the degree of quality of keywords in this resume and give suggestions for improvement about the potential missing keywords that user should add to the resume.
    Resume Text: {chunk}
    
    Analyze:
    1. How relevant are the resume's keywords to the professional profile?
    2. Are the keywords specific and demonstrate expertise or are they generic?
    3. Do the keywords highlight the candidate's strengths?
    4. Are keywords repetitive or diverse?
    5. Do keywords demonstrate technical skills and domain knowledge?

    After that, give suggestions for improvement about the potential missing keywords that user should add to the resume. (maximum 5 most relevant keywords)
    
    Give a quality score between 0 and 1 where:
    - 0.9-1.0: Excellent match with high-quality, specific keywords
    - 0.7-0.9: Good match with mostly strong keywords
    - 0.4-0.7: Average match with mix of strong and generic keywords
    - 0.1-0.4: Poor match with mostly generic keywords
    - 0-0.1: Very poor match with common, useless, or repetitive keywords
    
    Return your response with the following JSON format:
    {{
        "quality_score": float between 0 and 1,
        "missing_keywords": array of strings
    }}
    """
    
    # Process keyword quality evaluation with chunking
    quality_results = []
    for i, chunk in enumerate(text_chunks):
        logger.info(f"Processing keyword quality for chunk {i+1}/{len(text_chunks)}")
        
        chunk_prompt = keyword_quality_prompt
        
        try:
            keyword_quality_response = client_groq.chat.completions.create(
                model=MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert resume analyst."},
                    {"role": "user", "content": chunk_prompt}
                ],
                temperature=0.2,
                seed=5,
                response_format={"type": "json_object"}
            )
            
            # Extract quality score from the response
            quality_response_text = keyword_quality_response.choices[0].message.content
            
            # Clean up the response to get just the JSON part
            quality_response_text = quality_response_text.strip()
            if "```json" in quality_response_text:
                quality_response_text = quality_response_text.split("```json")[1].split("```")[0].strip()
            elif "```" in quality_response_text:
                quality_response_text = quality_response_text.split("```")[1].strip()
            
            quality_data = robust_json_parse(quality_response_text)
            quality_results.append(quality_data)
            
        except Exception as e:
            logger.error(f"Error processing keyword quality for chunk {i+1}: {e}")
            if 'quality_response_text' in locals():
                logger.error(f"Raw response text: {quality_response_text[:200]}...")
            # Add fallback result
            quality_results.append({"quality_score": 0, "missing_keywords": []})
    
    # Average the quality results
    if quality_results:
        averaged_quality = average_quality_results(quality_results)
        matching_jd_resume = averaged_quality.get("quality_score", 0)
        suggested_missing_keywords = averaged_quality.get("missing_keywords", [])
        if missing_keywords == [] and suggested_missing_keywords:
            missing_keywords = suggested_missing_keywords
    else:
        logger.error("No quality results available")
        # Fallback if API call fails - use the basic matching calculation
        matching_jd_resume = len(matching_keywords) / len(jd_keywords) if jd_keywords else 0
        if matching_jd_resume > 1: 
            matching_jd_resume = 1

    # Analyze repeated words and suggest synonyms
    repeated_words_prompt = f"""
    Analyze this resume text to identify frequently repeated words (especially verbs and adjectives) that could be replaced with synonyms to improve variety.
    
    For each frequently repeated word:
    1. List all instances/usages of the word in the text (at least 3 instances)
    2. Provide 2-3 appropriate synonyms that could be used as alternatives
    3. Include the definition of the word to ensure synonyms match the intended meaning
    4. Note whether each usage is as a verb or another part of speech
    
    Resume Text:
    {chunk}
    
    Return your analysis as a JSON object with these keys:
    - repeated_words: an object where each key is a repeated word, and each value contains:
      - usages: array of strings showing how the word appears in the text
      - synsets: array of objects with "synonyms" (array of objects with "lemma" and "inflections") and "definition"
      - usage_count: integer showing how many times the word appears
      - usages_as_verbs: array of booleans indicating if each usage is as a verb
    
    Only include words that appear at least 3 times and would benefit from synonym replacement.
    """
    
    # Process synonym analysis with chunking
    synonym_results = []
    for i, chunk in enumerate(text_chunks):
        logger.info(f"Processing synonym analysis for chunk {i+1}/{len(text_chunks)}")
        
        chunk_prompt = repeated_words_prompt
        
        try:
            synonym_response = client_groq.chat.completions.create(
                model=MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert in language analysis and vocabulary enhancement."},
                    {"role": "user", "content": chunk_prompt}
                ],
                temperature=0.2,
                seed=4
            )
            
            # Extract synonym analysis from the response
            synonym_text = synonym_response.choices[0].message.content
            # Clean up the response to get just the JSON part
            synonym_text = synonym_text.strip()
            if "```json" in synonym_text:
                synonym_text = synonym_text.split("```json")[1].split("```")[0].strip()
            elif "```" in synonym_text:
                synonym_text = synonym_text.split("```")[1].strip()
            
            synonym_data = robust_json_parse(synonym_text)
            synonym_results.append(synonym_data)
            
        except Exception as e:
            logger.error(f"Error processing synonym analysis for chunk {i+1}: {e}")
            if 'synonym_text' in locals():
                logger.error(f"Raw response text: {synonym_text[:200]}...")
            # Add fallback result
            synonym_results.append({"repeated_words": {}})
    
    # Combine synonym results from all chunks
    if synonym_results:
        averaged_synonyms = average_synonym_results(synonym_results)
        synonym_suggestions = averaged_synonyms.get("repeated_words", {})
    else:
        # Fallback if API call fails
        synonym_suggestions = {
            "ensure": {
                "usages": ["ensuring", "Ensured"],
                "synsets": [{"synonyms": [{"lemma": "guarantee", "inflections": [["guaranteeing"], ["Guaranteed"]]}], "definition": "make certain of"}],
                "usage_count": 2,
                "usages_as_verbs": [True, True]
            },
            "improve": {
                "usages": ["improved", "improved"],
                "synsets": [{"synonyms": [{"lemma": "better", "inflections": [["bettered"], ["bettered"]]}], "definition": "to make better"}],
                "usage_count": 2,
                "usages_as_verbs": [True, True]
            }
        }

    # Bullet point analysis
    num_bullets = count_bullets(sections)
    num_sections_with_bullets = count_sections_with_bullets(sections)
    bullets_per_section = get_bullets_per_section(sections)
    if bullets_per_section:
        max_num_bullets_in_section = max(bullets_per_section.values())
        min_num_bullets_in_section = min(bullets_per_section.values())
        min_non_zero = min((v for v in bullets_per_section.values() if v > 0), default=0)
        avg_num_bullets = sum(bullets_per_section.values()) / len(bullets_per_section)
    else:
        max_num_bullets_in_section = 0
        min_num_bullets_in_section = 0
        min_non_zero = 0
        avg_num_bullets = 0

    # Bold text analysis
    num_bolds, length_bold_text_chars = count_bolds_and_length(sections)

    # Pronoun count
    # tagged_words = pos_tag(words)
    pronouns = [
        "i", "me", "my", "mine", "myself",
        "you", "your", "yours", "yourself",
        "he", "him", "his", "himself",
        "she", "her", "hers", "herself",
        "it", "its", "itself",
        "we", "us", "our", "ours", "ourselves",
        "they", "them", "their", "theirs", "themselves"
    ]
    # num_pronouns = len([w for w, tag in tagged_words if tag in ['PRP', 'PRP$']])
    num_pronouns = sum(1 for word in words if word in pronouns)

    # Define phrase lists based on output schema
    phrase_categories = {
        "v2_sales": ["Developed", "improved", "enhance", "Analyzed", "Collaborated", "Presented"],
        "v2_helping": ["improved", "informed", "facilitated"],
        "v2_teaching": ["Developed", "informed", "facilitated"],
        "v2_technical": ["machine learning", "Analyzed", "Collaborated"],
        "v2_good_words": ["Developed", "model", "using", "improved", "helping", "Analyzed", "leading", "informed", "Presented", "facilitated", "project"],
        "v2_weak_action": ["using", "helping", "buy", "lost", "Feeling"],
        "v2_filler_words": ["which", "in", "the", "that", "and", "good", "kind of"],
        "v2_accomplishments": ["Developed", "improved", "Analyzed", "Collaborated", "facilitated"],
        "v2_action_oriented": ["Developed", "improved", "Analyzed", "informed", "Collaborated", "Presented", "facilitated"],
        "v2_analysis_skills": ["Analyzed", "Presented"],
        "v2_creative_skills": ["Developed", "model", "using", "improved", "enhance", "Analyzed", "extract", "leading", "informed", "increase", "Collaborated", "integrate", "ensuring", "improved", "facilitated", "driving", "project", "Exploring"],
        "v2_problem_solving": ["Developed", "improved", "Analyzed"],
        "v2_financial_skills": ["Developed", "Analyzed", "facilitated"],
        "v2_management_skills": ["Developed", "improved", "Analyzed", "Collaborated", "facilitated"],
        "v2_mentorship_skills": ["Developed", "improved", "Analyzed", "informed", "Presented", "facilitated"],
        "v2_operations_skills": ["Developed", "improved", "Analyzed", "Collaborated", "Presented", "facilitated"],
        "v2_communication_skills": ["Developed", "improved", "leading", "Collaborated", "integrate", "Presented", "facilitated", "project"],
        "v2_detail_oriented_skills": ["model", "Analyzed", "key", "integrate"],
        "v2_entrepreneurial_skills": ["Developed", "decision-making", "Analyzed", "leading", "driving", "project"],
        "v2_information_technology": ["machine learning", "project"],
        "v2_strong_accomplishment_verb": ["Developed", "improved", "Analyzed"]
    }
    
    # Additional phrase categories that were missing
    phrase_categories.update({
        "v2_cliches": ["team player", "hard worker", "detail-oriented", "self-starter", "go-getter", "think outside the box"],
        "v2_education": ["graduated", "degree", "diploma", "certificate", "studied", "major", "minor"],
        "v2_good_work": ["achieved", "delivered", "implemented", "launched", "spearheaded", "orchestrated"],
        "v2_responsibility": ["responsible for", "duties included", "in charge of", "managed", "oversaw", "supervised"]
    })

    # Find used phrases and their counts
    used_phrases = {cat: find_phrases(text, phrases) for cat, phrases in phrase_categories.items()}
    phrase_counts = {f"num_{cat}_phrases_found": len(used_phrases[cat]) for cat in phrase_categories}

    # Basic feature flags
    has_email = bool(basics.get("email"))
    has_phone = bool(basics.get("phone"))
    has_web = bool(basics.get("url", {}).get("href"))
    has_summary = bool(sections.get("summary", {}).get("content"))
    has_skills = bool(sections.get("skills", {}).get("items"))
    has_education = bool(sections.get("education", {}).get("items"))
    has_hobbies = bool(sections.get("hobbies", {}).get("items"))
    fullname = basics.get("fullname", "").split()
    has_first_name = len(fullname) >= 1
    has_last_name = len(fullname) >= 2


    # Count meaningful words (non-stop words with length > 2)
    num_meaningful_words = len([w for w in non_stop_words if len(w) > 2])

    # Extract and analyze dates from experience section
    experience_items = sections.get("experience", {}).get("items", [])
    missing_start_dates = 0
    missing_end_dates = 0
    num_possible_dates = 0
    
    for item in experience_items:
        if "startDate" in item:
            num_possible_dates += 1
        else:
            missing_start_dates += 1
            
        if "endDate" in item:
            num_possible_dates += 1
        else:
            missing_end_dates += 1
    
    missing_start_dates_ratio = missing_start_dates / len(experience_items) if experience_items else 0
    missing_end_dates_ratio = missing_end_dates / len(experience_items) if experience_items else 0
    
    # Calculate experience description word counts
    experience_word_counts = []
    for item in experience_items:
        if "summary" in item:
            item_text = BeautifulSoup(item["summary"], "html.parser").get_text()
            item_words = re.findall(r'\b[a-zA-Z]+\b', item_text)
            experience_word_counts.append(len(item_words))
    
    if experience_word_counts:
        max_experience_words = max(experience_word_counts)
        min_experience_words = min(experience_word_counts)
        average_experience_words = sum(experience_word_counts) / len(experience_word_counts)
    else:
        max_experience_words = 0
        min_experience_words = 0
        average_experience_words = 0

    # Placeholder for num_words_general_quick (assuming it's a count of general/quick words)
    general_quick_words = ["quickly", "efficiently", "effectively", "generally", "usually", "typically"]
    num_words_general_quick = sum(1 for w in words_lower if w in general_quick_words)

    # Assemble features dictionary
    features = {
        "ATS_score": 0,
        "has_web": has_web,
        "has_work": bool(sections.get("experience", {}).get("items")),  # Assuming 'experience' is work
        "language": "en",
        "has_email": has_email,
        "has_phone": has_phone,
        "num_bolds": num_bolds,
        "num_words": num_words,
        "has_skills": has_skills,
        "has_hobbies": has_hobbies,
        "has_summary": has_summary,
        "num_bullets": num_bullets,
        "num_pronouns": num_pronouns,
        "has_education": has_education,
        "has_last_name": has_last_name,
        "passive_voice": passive_voice,
        "has_first_name": has_first_name,
        "num_characters": num_characters,
        "num_stop_words": num_stop_words,
        "num_unique_words": num_unique_words,
        "missing_end_dates": missing_end_dates,  # Now calculated
        "unique_percentage": unique_percentage,
        "num_possible_dates": num_possible_dates,  # Now calculated
        "missing_start_dates": missing_start_dates,  # Now calculated
        "synonym_suggestions": synonym_suggestions,
        "max_experience_words": max_experience_words,  # Now calculated
        "min_experience_words": min_experience_words,  # Now calculated
        "num_meaningful_words": num_meaningful_words,  # Now calculated
        "length_bold_text_chars": length_bold_text_chars,
        "max_bullet_length_chars": max_bullet_length_chars,
        "min_bullet_length_chars": min_bullet_length_chars,
        "missing_end_dates_ratio": missing_end_dates_ratio,  # Now calculated
        "num_words_general_quick": num_words_general_quick,  # Now calculated
        "average_experience_words": average_experience_words,  # Now calculated
        "missing_start_dates_ratio": missing_start_dates_ratio,  # Now calculated
        "num_sections_with_bullets": num_sections_with_bullets,
        "num_unique_non_stop_words": num_unique_non_stop_words,
        "max_num_bullets_in_section": max_num_bullets_in_section,
        "min_num_bullets_in_section": min_num_bullets_in_section,
        "unique_non_stop_percentage": unique_non_stop_percentage,
        "average_bullet_length_chars": average_bullet_length_chars,
        "num_experience_descriptions": len(sections.get("experience", {}).get("items", [])),
        "average_num_bullets_per_section": avg_num_bullets,
        "min_non_zero_num_bullets_in_section": min_non_zero,
        "jd_keywords": jd_keywords,
        "matching_jd_resume": matching_jd_resume,
        "professional_score": professional_score,
        "missing_keywords": missing_keywords,  # Added missing keywords field
    }

    # return {"features_and_ratings": {"features": features}}

    RESULTS = {"features_and_ratings": {"features": features}}
    ATS_score, keyword_score, structure_score, content_quality_score = calculate_ats_score(RESULTS)
    
    # Log final results
    logger.info(f"Resume analysis completed. Used {len(text_chunks)} chunks. Final ATS score: {ATS_score}")
    
    if is_free_user:
        return {
            "ATS_score": ATS_score,
            "keyword_score": keyword_score,
            "structure_score": structure_score,
            "content_quality_score": content_quality_score
        }
    features["ATS_score"] = ATS_score
    features["keyword_score"] = keyword_score
    features["structure_score"] = structure_score
    features["content_quality_score"] = content_quality_score
    return features


    
# if __name__ == "__main__":
#     import json
#     # Example resume JSON (simplified)
#     with open(r"C:\Users\<USER>\OneDrive\Máy tính\reactive_resume-ffa0e76b-35f4-423d-bb27-88c3ee977e89.json", "r") as f:
#         resume_details_js = json.load(f)
#     jd_content = """About the jobOur mission and where you fit inAt Employment Hero, we're an ambitious bunch of people on a mission to make employment easier and more valuable for everyone.Our world-class software is the easiest way for businesses to manage HR, payroll, employee engagement, and benefits.Since our inception in 2014, we've had some pretty impressive growth (100% YoY), reached unicorn status in 2022, and now serve 400,000 businesses globally, with 3 million+ users on the platform. We have no plans to slow down.There's never been a more exciting time to join one of the fastest-growing SaaS unicorns, so let's see if we could be a match!As a Machine Learning Engineer, you will play a pivotal role in advancing our AI efforts. You will have expert-level understanding of LLMs and stay up to date with the latest developments in this space to build scalable, robust, and high-performance AI systems. Your deep technical expertise and business acumen will be crucial in building AI solutions to the identified problems and driving business growth.In your role, you'll be focused onDesign and build working prototype/s for machine learning and AI applicationsArchitect and implement scalable AI systems and pipelines, ensuring seamless integration with existing infrastructureDesign an evaluation framework to track the system performance online and offlineWork closely with cross-functional teams, including software engineers and product managers, to build AI solutions that solve customer problemsProvide an technical guidance and mentorship to other ML engineers, fostering a culture of continuous learning and innovationRequirementsYou're the hero we're looking for if3+ years of experience in machine learning, with a strong focus on applied AI and large language modelsStrong experience with programming languages, techniques and frameworks aligned with machine learning. e.g. Python, LLM, OpenAI, LangChain, LlamaIndex, Vector DB, RAG, HuggingFace, NLP, Recommender System, ChatbotExperience with Generative AI, Prompt Engineering and Large Language ModelsBe curious about the latest developments in AI research, continuously exploring new methodologies, tools, and techniques to enhance our AI capabilitiesExperience using Agile methodologies and working with Product teamsQuality coding practices, including test-driven development, unit testing and secure coding awarenessYou are passionate about learning and sharing your knowledge, and not afraid to challenge your peers, but also welcome being challengedEnglish language abilities, both written and verbal - you'll be working with people across the worldExperience is important, but for us the biggest measure of success is people who can live and breathe our values. Show us what you can bring to the table, and we'll empower you to let your talents shine.BenefitsThe EH WayThe EH Way is how we describe our culture at Employment Hero and how we all operate. It is our DNA. You can read all about it on our careers page https//employmenthero.com/careers/In short, you'll love working with us ifRevolutionising employment gets your heart racingYou thrive on the flexibility (and responsibility) of a remote-first businessOur values align, and shape how you show up every dayYou love the dynamic pace of a startup, are driven by innovation, and enjoy working with other smart peopleBut don't just take it from us, hear from your local heroes{{:}} Thao Ta, Head of People and Culture & Hung Pham, Group Engineer Manager Life at Employment Hero | VietnamPlus, you'll get to enjoy a number of great perks, includingA generous budget for your home officeCutting-edge tools and technology20 days Annual Leave, plus VN Public Holidays$500 USD for your professional development plan$500 USD for English learning coursesPremium Healthcare Insurance Program for you and your loved ones, plus full gross salary paid social insuranceSports club funded by Employment HeroMonthly get-together event in the office for team bonding and VND 80,000 budget for lunch for day-in-officeReward and recognition programs - because great work should be recognised and rewardedEmployee Share Option Program - be an owner of Employment HeroAnnual Global Gathering - so far we've been to Thailand, Vietnam, Bali and are excited to meet in Dubai in 2025Are we a match? Think we're the right match for you? Fantastic! Click 'Apply' and our talent team will reach out with the next steps.At Employment Hero, we are committed to safeguarding the privacy of your application data. To understand how we do so, you can read our Applicant Privacy Policy here{{:}} https//employmenthero.com/legals/applicant-policy/Employment Hero celebrates diverse perspectives and experiences, we invite people of all backgrounds and identities to apply for this position."""
#     result = analyze_resume(resume_details_js, jd_content)
#     print(json.dumps(result, indent=2))
