import re
import html
from typing import Optional


def html_to_markdown(html_text: str) -> str:
    """
    Convert HTML text to Markdown format.
    
    Supports:
    - Headers (h1-h6 to # ## ### etc.)
    - Bold and italic text (strong/b, em/i)
    - Unordered lists (ul/li to - or *)
    - Ordered lists (ol/li to 1. 2. etc.)
    - Links (a href to [text](url))
    - Code blocks (pre/code to ```code```) and inline code (code to `code`)
    - Line breaks (br) and paragraphs (p)
    - Horizontal rules (hr to ---)
    
    Args:
        html_text (str): The HTML text to convert
        
    Returns:
        str: Markdown formatted text
    """
    if not html_text or not isinstance(html_text, str):
        return ""
    
    # Start with the original text
    markdown_text = html_text
    
    # Convert code blocks first (before other processing)
    markdown_text = _convert_html_code_blocks(markdown_text)
    
    # Convert inline code
    markdown_text = _convert_html_inline_code(markdown_text)
    
    # Convert headers
    markdown_text = _convert_html_headers(markdown_text)
    
    # Convert horizontal rules
    markdown_text = re.sub(r'<hr\s*/?>', '---', markdown_text, flags=re.IGNORECASE)
    
    # Convert bold and italic text
    markdown_text = _convert_html_emphasis(markdown_text)
    
    # Convert links
    markdown_text = _convert_html_links(markdown_text)
    
    # Convert lists
    markdown_text = _convert_html_lists(markdown_text)
    
    # Convert paragraphs and line breaks
    markdown_text = _convert_html_paragraphs(markdown_text)
    
    # Clean up any remaining HTML tags
    markdown_text = _remove_remaining_html_tags(markdown_text)
    
    # Unescape HTML entities
    markdown_text = html.unescape(markdown_text)
    
    return markdown_text.strip()


def _convert_html_code_blocks(text: str) -> str:
    """Convert HTML code blocks to Markdown."""
    # Convert <pre><code class="language-xxx">...</code></pre> to ```xxx\n...\n```
    pattern = r'<pre><code(?:\s+class="language-(\w+)")?>(.*?)</code></pre>'
    def replace_code_block(match):
        language = match.group(1) or ''
        code_content = match.group(2).strip()
        if language:
            return f'```{language}\n{code_content}\n```'
        else:
            return f'```\n{code_content}\n```'
    
    return re.sub(pattern, replace_code_block, text, flags=re.DOTALL | re.IGNORECASE)


def _convert_html_inline_code(text: str) -> str:
    """Convert HTML inline code to Markdown."""
    return re.sub(r'<code>(.*?)</code>', r'`\1`', text, flags=re.IGNORECASE)


def _convert_html_headers(text: str) -> str:
    """Convert HTML headers to Markdown."""
    # Convert h1-h6 tags to markdown headers
    for level in range(1, 7):
        pattern = rf'<h{level}(?:[^>]*)>(.*?)</h{level}>'
        replacement = '#' * level + r' \1'
        text = re.sub(pattern, replacement, text, flags=re.IGNORECASE | re.DOTALL)
    
    return text


def _convert_html_emphasis(text: str) -> str:
    """Convert HTML bold and italic text to Markdown."""
    # Convert strong/b tags to **text**
    text = re.sub(r'<(?:strong|b)(?:[^>]*)>(.*?)</(?:strong|b)>', r'**\1**', text, flags=re.IGNORECASE | re.DOTALL)
    
    # Convert em/i tags to *text*
    text = re.sub(r'<(?:em|i)(?:[^>]*)>(.*?)</(?:em|i)>', r'*\1*', text, flags=re.IGNORECASE | re.DOTALL)
    
    return text


def _convert_html_links(text: str) -> str:
    """Remove HTML links and keep only the text content."""
    # Remove <a href="url">text</a> and keep only the text
    pattern = r'<a\s+(?:[^>]*\s+)?href="([^"]*)"(?:[^>]*)>(.*?)</a>'
    return re.sub(pattern, r'\2', text, flags=re.IGNORECASE | re.DOTALL)


def _convert_html_lists(text: str) -> str:
    """Convert HTML lists to Markdown."""
    # Handle unordered lists
    text = _convert_html_unordered_lists(text)
    
    # Handle ordered lists
    text = _convert_html_ordered_lists(text)
    
    return text


def _convert_html_unordered_lists(text: str) -> str:
    """Convert HTML unordered lists to Markdown."""
    # Find all ul blocks and convert them
    def replace_ul(match):
        ul_content = match.group(1)
        # Extract li items
        li_items = re.findall(r'<li(?:[^>]*)>(.*?)</li>', ul_content, flags=re.IGNORECASE | re.DOTALL)
        markdown_items = []
        for item in li_items:
            # Clean up the item content and strip any HTML tags
            item = re.sub(r'<[^>]+>', '', item).strip()
            markdown_items.append(f'- {item}')
        return '\n'.join(markdown_items)
    
    pattern = r'<ul(?:[^>]*)>(.*?)</ul>'
    return re.sub(pattern, replace_ul, text, flags=re.IGNORECASE | re.DOTALL)


def _convert_html_ordered_lists(text: str) -> str:
    """Convert HTML ordered lists to Markdown."""
    # Find all ol blocks and convert them
    def replace_ol(match):
        ol_content = match.group(1)
        # Extract li items
        li_items = re.findall(r'<li(?:[^>]*)>(.*?)</li>', ol_content, flags=re.IGNORECASE | re.DOTALL)
        markdown_items = []
        for i, item in enumerate(li_items, 1):
            # Clean up the item content and strip any HTML tags
            item = re.sub(r'<[^>]+>', '', item).strip()
            markdown_items.append(f'{i}. {item}')
        return '\n'.join(markdown_items)
    
    pattern = r'<ol(?:[^>]*)>(.*?)</ol>'
    return re.sub(pattern, replace_ol, text, flags=re.IGNORECASE | re.DOTALL)


def _convert_html_paragraphs(text: str) -> str:
    """Convert HTML paragraphs and line breaks to Markdown."""
    # Convert <br> tags to newlines
    text = re.sub(r'<br\s*/?>', '\n', text, flags=re.IGNORECASE)
    
    # Convert <p> tags - remove opening and closing tags but preserve content
    text = re.sub(r'<p(?:[^>]*)>(.*?)</p>', r'\1\n\n', text, flags=re.IGNORECASE | re.DOTALL)
    
    # Clean up multiple consecutive newlines
    text = re.sub(r'\n{3,}', '\n\n', text)
    
    return text


def _remove_remaining_html_tags(text: str) -> str:
    """Remove any remaining HTML tags."""
    # Remove any remaining HTML tags
    text = re.sub(r'<[^>]+>', '', text)
    return text


def clean_html_to_markdown(html_text: str) -> str:
    """
    A simpler version that focuses on basic formatting suitable for resume content.
    
    Args:
        html_text (str): The HTML text to convert
        
    Returns:
        str: Clean Markdown formatted text
    """
    if not html_text or not isinstance(html_text, str):
        return ""
    
    # Start with the original text
    markdown_text = html_text
    
    # Convert headers to markdown headers
    for level in range(1, 7):
        pattern = rf'<h{level}(?:[^>]*)>(.*?)</h{level}>'
        replacement = '#' * level + r' \1'
        markdown_text = re.sub(pattern, replacement, markdown_text, flags=re.IGNORECASE | re.DOTALL)
    
    # Convert strong/b tags to **text**
    markdown_text = re.sub(r'<(?:strong|b)(?:[^>]*)>(.*?)</(?:strong|b)>', r'**\1**', markdown_text, flags=re.IGNORECASE | re.DOTALL)
    
    # Convert em/i tags to *text*
    markdown_text = re.sub(r'<(?:em|i)(?:[^>]*)>(.*?)</(?:em|i)>', r'*\1*', markdown_text, flags=re.IGNORECASE | re.DOTALL)
    
    # Convert unordered lists
    def replace_ul_simple(match):
        ul_content = match.group(1)
        li_items = re.findall(r'<li(?:[^>]*)>(.*?)</li>', ul_content, flags=re.IGNORECASE | re.DOTALL)
        markdown_items = []
        for item in li_items:
            item = re.sub(r'<[^>]+>', '', item).strip()
            markdown_items.append(f'- {item}')
        return '\n'.join(markdown_items)
    
    pattern = r'<ul(?:[^>]*)>(.*?)</ul>'
    markdown_text = re.sub(pattern, replace_ul_simple, markdown_text, flags=re.IGNORECASE | re.DOTALL)
    
    # Convert ordered lists
    def replace_ol_simple(match):
        ol_content = match.group(1)
        li_items = re.findall(r'<li(?:[^>]*)>(.*?)</li>', ol_content, flags=re.IGNORECASE | re.DOTALL)
        markdown_items = []
        for i, item in enumerate(li_items, 1):
            item = re.sub(r'<[^>]+>', '', item).strip()
            markdown_items.append(f'{i}. {item}')
        return '\n'.join(markdown_items)
    
    pattern = r'<ol(?:[^>]*)>(.*?)</ol>'
    markdown_text = re.sub(pattern, replace_ol_simple, markdown_text, flags=re.IGNORECASE | re.DOTALL)
    
    # Convert links
    pattern = r'<a\s+(?:[^>]*\s+)?href="([^"]*)"(?:[^>]*)>(.*?)</a>'
    markdown_text = re.sub(pattern, r'\2', markdown_text, flags=re.IGNORECASE | re.DOTALL)
    
    # Convert line breaks
    markdown_text = re.sub(r'<br\s*/?>', '\n', markdown_text, flags=re.IGNORECASE)
    
    # Convert paragraphs
    markdown_text = re.sub(r'<p(?:[^>]*)>(.*?)</p>', r'\1\n\n', markdown_text, flags=re.IGNORECASE | re.DOTALL)
    
    # Remove any remaining HTML tags
    markdown_text = re.sub(r'<[^>]+>', '', markdown_text)
    
    # Clean up multiple consecutive newlines
    markdown_text = re.sub(r'\n{3,}', '\n\n', markdown_text)
    
    # Unescape HTML entities
    markdown_text = html.unescape(markdown_text)
    
    return markdown_text.strip()


def extract_text_from_html(html_text: str) -> str:
    """
    Extract plain text from HTML by removing all tags and converting entities.
    
    Args:
        html_text (str): The HTML text to extract from
        
    Returns:
        str: Plain text with HTML tags removed
    """
    if not html_text or not isinstance(html_text, str):
        return ""
    
    # Remove script and style elements completely
    text = re.sub(r'<(script|style)[^>]*>.*?</\1>', '', html_text, flags=re.IGNORECASE | re.DOTALL)
    
    # Convert line breaks to newlines before removing tags
    text = re.sub(r'<br\s*/?>', '\n', text, flags=re.IGNORECASE)
    
    # Add newlines around block elements
    block_elements = ['div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'tr']
    for element in block_elements:
        text = re.sub(rf'</{element}(?:[^>]*)>', '\n', text, flags=re.IGNORECASE)
    
    # Remove all remaining HTML tags
    text = re.sub(r'<[^>]+>', '', text)
    
    # Unescape HTML entities
    text = html.unescape(text)
    
    # Clean up whitespace
    text = re.sub(r'\n{3,}', '\n\n', text)  # Remove excessive newlines
    text = re.sub(r'[ \t]+', ' ', text)      # Normalize spaces
    text = text.strip()
    
    return text


# if __name__ == "__main__":
#     # Example HTML content for testing
#     sample_html = """
#     <h1>Software Engineer</h1>
#     <h2>Experience</h2>
#     <p>I have <strong>5 years</strong> of experience in <em>web development</em>.</p>
    
#     <h3>Skills</h3>
#     <ul>
#         <li>Python programming</li>
#         <li><strong>Django</strong> framework</li>
#         <li>JavaScript and <code>React</code></li>
#     </ul>
    
#     <h3>Recent Projects</h3>
#     <ol>
#         <li>E-commerce platform with <a href="https://django.com">Django</a></li>
#         <li>API development using FastAPI</li>
#         <li>Frontend with React and TypeScript</li>
#     </ol>
    
#     <pre><code class="language-python">
# def hello_world():
#     print("Hello, World!")
#     </code></pre>
    
#     <p>Contact me at: <a href="mailto:<EMAIL>"><EMAIL></a></p>
#     <hr>
#     <p>Thank you for reading!</p>
#     """
    
#     print("=" * 60)
#     print("HTML TO MARKDOWN CONVERTER EXAMPLES")
#     print("=" * 60)
    
#     # Example 1: Full HTML to Markdown conversion
#     print("\n1. FULL HTML TO MARKDOWN CONVERSION:")
#     print("-" * 40)
#     print("Input HTML:")
#     print(sample_html)
#     print("\nOutput Markdown:")
#     markdown_result = html_to_markdown(sample_html)
#     print(markdown_result)
    
#     # Example 2: Clean HTML to Markdown conversion
#     print("\n" + "=" * 60)
#     print("\n2. CLEAN HTML TO MARKDOWN CONVERSION:")
#     print("-" * 40)
#     print("Output Clean Markdown:")
#     clean_markdown_result = clean_html_to_markdown(sample_html)
#     print(clean_markdown_result)
    
#     # Example 3: Extract plain text from HTML
#     print("\n" + "=" * 60)
#     print("\n3. EXTRACT PLAIN TEXT FROM HTML:")
#     print("-" * 40)
#     print("Output Plain Text:")
#     plain_text_result = extract_text_from_html(sample_html)
#     print(plain_text_result)
    
#     # Example 4: Simple HTML examples
#     print("\n" + "=" * 60)
#     print("\n4. SIMPLE HTML EXAMPLES:")
#     print("-" * 40)
    
#     simple_examples = [
#         ("<p>This is <strong>bold</strong> and <em>italic</em> text.</p>", "Bold and italic"),
#         ("<ul><li>Item 1</li><li>Item 2</li></ul>", "Unordered list"),
#         ("<h2>Title</h2><p>Some content with <code>inline code</code>.</p>", "Header with inline code"),
#         ("<a href='https://example.com'>Visit Example</a>", "Simple link"),
#     ]
    
#     for html_input, description in simple_examples:
#         print(f"\n{description}:")
#         print(f"HTML: {html_input}")
#         print(f"Markdown: {html_to_markdown(html_input)}")
#         print(f"Plain text: {extract_text_from_html(html_input)}")
    
#     print("\n" + "=" * 60)
#     print("Examples completed!") 