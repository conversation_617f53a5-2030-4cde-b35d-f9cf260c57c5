import os
import random
import json
import time
import re
from groq import Groq
from repositories.job_description import JobDescriptionQuery, JobDescriptionRepository
from repositories.resume import ResumeQuery, ResumeRepository
from sanic.log import logger
from utils import llm_prompts
from pydantic import BaseModel, <PERSON>
from typing import Optional
from api.base import json_response, Response, ResponseCode
from langchain_community.utilities import GoogleSerperAPIWrapper
from repositories.application_kit import ApplicationKitRepository, ApplicationKitQuery
from repositories.base import Pagination
from repositories.user import UserRepository
import requests
from utils.markdown_converter import clean_markdown_to_html
from urllib.parse import urlparse

def check_groq_model_availability(model_name: str) -> bool:
    client_groq = Groq(api_key=os.environ.get("GROQ_API_KEY"))
    try:
        model = client_groq.models.retrieve(model_name)
        return model.active
    except Exception as e:
        logger.error(f"Error checking model availability: {e}")
        return False

def get_available_groq_model(priority_model = "") -> str:
    """
    Check and return available GROQ model, prioritizing quick model over standard model.

    Returns:
        str: Name of available GROQ model

    Raises:
        HTTPResponse: If no models are available, returns 503 error response
    """
    if priority_model != "":
        if check_groq_model_availability(priority_model):
            return priority_model
    if check_groq_model_availability(os.environ.get("GROQ_QUICK_MODEL")):
        return os.environ.get("GROQ_QUICK_MODEL")
    elif check_groq_model_availability(os.environ.get("GROQ_STANDARD_MODEL")):
        return os.environ.get("GROQ_STANDARD_MODEL")
    else:
        logger.error("LLM models are not available at the moment")
        return json_response(Response(
                error=ResponseCode.CLIENT_BAD_REQUEST,
                message="Error with Groq model",
                data={"error": "LLM models are not available at the moment"}
            ), status_code=503)

async def get_filtered_resume_content(resume_id: str) -> str:
    """
    Retrieves and filters resume content to remove unnecessary fields and empty sections.

    Args:
        resume_id (str): The ID of the resume to retrieve

    Returns:
        str: JSON string of filtered resume content

    Raises:
        BadRequest: If resume cannot be found or accessed
    """
    # Get resume data
    resume_query = ResumeQuery(id=resume_id)
    resume_repo = await ResumeRepository.get_one(resume_query)
    if not resume_repo:
        return "Resume not found"
    resume_content = resume_repo['content']

    # Filter basic information
    filtered_basics = {
        k: v for k, v in resume_content['basics'].items()
        if v and k not in ['url', 'picture', 'customFields']
    }

    # Filter resume sections
    filtered_sections = {}
    for section_name, section in resume_content['sections'].items():
        if section_name != 'custom':
            if section.get('items') and len(section['items']) > 0:
                filtered_sections[section_name] = section
            elif section.get('content') and section['content']:
                filtered_sections[section_name] = section

    # Combine filtered content
    filtered_content = {
        'basics': filtered_basics,
        'sections': filtered_sections
    }

    return json.dumps(filtered_content)

def replace_punctuations(text: str) -> str:
    # Mapping of symbols to words
    replacements = {
        r'/': ' or ',
        r'&': ' and ',
        r'\+': ' and ',
        r'\|': ' or ',
        r'–': ' to ',     # en‑dash
        r'—': ', ',      # em‑dash
        r';': ', and '   # often replaces ";"
    }

    # Do all substitutions
    for pattern, repl in replacements.items():
        text = re.sub(pattern, repl, text)

    # Clean up any excess whitespace
    return re.sub(r'\s{2,}', ' ', text).strip()

async def get_job_description(job_description_id: str):
    """
    This function retrieves a job description based on the provided job description id.
    It constructs a string containing the job title, required skills, job description, and location.
    """
    logger.info(f"Getting job description with id: {job_description_id}")
    job_description_query = JobDescriptionQuery(id=job_description_id)
    job_description_repo = await JobDescriptionRepository.get_one(job_description_query)
    if not job_description_repo:
        return "Job description not found"
    job_description = f"Job Title: {job_description_repo.position}\nSkills required: {', '.join(job_description_repo.skills)}\nJob Description: {job_description_repo.description}\nLocation: {job_description_repo.city}, {job_description_repo.country}"
    return job_description, job_description_repo

class JobLocation(BaseModel):
    city: Optional[str] = Field(default="", description="The city of the job, default is null")
    country: str = Field(default="USA", description="The country of the job, default is USA")

class JobDesire(BaseModel):
    job_title: str = Field(default="Unknown", description="The title of the job position")
    job_level: str = Field(default="Junior", description="The level of the job, e.g. 'Senior', 'Middle', 'Junior'")
    job_location: JobLocation

def extract_job_details(job_description: str) -> dict:
    """
    Extract job title, level and location details from a job description using LLM.

    Args:
        job_description (str): The job description text

    Returns:
        dict: Extracted job details including title, level and location
    """
    GROQ_MODEL = get_available_groq_model()
    client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))
    job_desire = client_groq.chat.completions.create(
        model=GROQ_MODEL,
        messages=[
            {
                "role": "system",
                "content": llm_prompts.EXTRACT_JOB_DETAILS_PROMPT,
            },
            {
                "role": "user",
                "content": job_description
            }
        ],
        seed=100,
        response_format={"type": "json_object"}
    )
    job_desire_json = JobDesire.model_validate_json(job_desire.choices[0].message.content)
    return json.loads(job_desire_json.model_dump_json())

class ResearchQuestions(BaseModel):
    topic: str = Field(default="Unknown", description="The topic of the research")
    research_questions: list[str] = Field(default=[], description="The list of research questions")

def generate_research_questions(job_desire: str, topic: str) -> list[str]:
    """
    Generate research questions based on job description and topic using LLM.

    Args:
        job_desire (str): The job desire text
        topic (str): The topic to generate questions about

    Returns:
        list[str]: List of research questions
    """
    time_start = time.time()
    client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))
    logger.info(f"Generating research questions....")
    completion = client_groq.chat.completions.create(
        model=get_available_groq_model(),
        temperature=0.9,
        seed=23,
        top_p=1,
        presence_penalty=0,
        response_format={"type": "json_object"},
        messages=[
            {
                "role": "system",
                "content": """You are an expert recruiter who has worked in the Talent Acquisition industry for more than 15 years.
Your responsibility is to recommend to users 10-20 questions related to the user's topic. These questions help users have a crystal direction for researching the relevant experience, working information or other information in the Internet.
You help and consult the users on their career path, but based on the specific topic that is given to you.
Your questions must be highly correlated to the user topic, because users never want to waste time reading you ridiculous ideas.
Your response must always be in the JSON format with double quotes:
{
    "topic": "string",      
    "research_questions": ["string1", "string2", ...]
}"""
            },
            {
                "role": "user",
                "content": f"10 insightful questions (and must be highly correlated) about topic: '{topic}'. These questions should be relevant to this Job description because I'm optimizing to be the best candidate for this job: {job_desire}"
            },
            {
                "role": "user",
                "content": "Please response in JSON format with double quotes."
            }
        ]
    )
    response = ResearchQuestions.model_validate_json(completion.choices[0].message.content)
    logger.info(f"Research questions for topic {topic}: {response}")
    time_end = time.time()
    logger.info(f"Time taken for generating questions for the section {topic}: {time_end - time_start} seconds")
    return json.loads(response.model_dump_json())
def search_information_based_on_jd(job_description, edit_zone, is_custom_section=False):
    if job_description == "" or edit_zone == "":
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Error",
            data={"error": "Job title and edit zone are required"}
        ), status_code=422)
    result_google_dict={} # store the result of the google search
    search = GoogleSerperAPIWrapper(serper_api_key=os.environ.get('SERPER_API_KEY'))
    job_details = extract_job_details(job_description)
    job_desire = f"{job_details['job_title']} {job_details['job_level']} in {job_details['job_location']['city']} {job_details['job_location']['country']}"
    if is_custom_section:
        logger.info("Searching Internet with Serper for the given topic.")
        generated_research_questions_json = generate_research_questions(job_desire, edit_zone)
        research_questions_list = generated_research_questions_json['research_questions']
        result_google_dict[edit_zone] = []
        for question in research_questions_list:
            result = search.run(question)
            result_google_dict[edit_zone].append(result)
    else:
        logger.info("Searching Internet with Serper for the given topic: {}".format(job_desire))
        for key, values_search_list in llm_prompts.SEARCH_GOOGLE_PROMPT.items():
            result_google_dict[key] = []
            if key == edit_zone or edit_zone == '':
                for value in values_search_list:
                    result = search.run(value.format(job_desire=job_desire))
                    result_google_dict[key].append(result)
    return result_google_dict


# Example usage with the fast lookup function
def find_item(json_data: dict, item_id: str, edit_zone: str) -> dict | None:
    try:
        return next(
            item for item in json_data["sections"][edit_zone]["items"]
            if item["id"] == item_id
        )
    except (KeyError, StopIteration):
        return None

def get_sections_and_reasons(missing_required_keys: list[str], missing_good_to_have_keys: list[str]) -> dict:
    """
    Get sections and reasons for missing required and good to have keys.
    """
    # Get the sections and reasons for missing required and good to have keys
    sections_and_reasons_result = {
        "missing_required_sections": missing_required_keys,
        "reason_of_adding_required_sections": [],
        "missing_good_to_have_sections": missing_good_to_have_keys,
        "reason_of_adding_good_to_have_sections": []
    }

    # Get the sections and reasons for missing required and good to have keys
    for missing_required_section in missing_required_keys:
        sections_and_reasons_result["reason_of_adding_required_sections"].append(
            random.choice(llm_prompts.RESUME_ADJUSTMENT_LOGIC_1_RECOMMENDATIONS[missing_required_section])
        )
    for missing_good_to_have_section in missing_good_to_have_keys:
        sections_and_reasons_result["reason_of_adding_good_to_have_sections"].append(
            random.choice(llm_prompts.RESUME_ADJUSTMENT_LOGIC_1_RECOMMENDATIONS[missing_good_to_have_section])
        )
    return sections_and_reasons_result

def get_included_recommendations(required_keys: list[str], good_to_have_keys: list[str]) -> dict:
    """
    Get included recommendations for the given sections.
    """
    # Get the sections and reasons for missing required and good to have keys
    sections_and_reasons_result = {
        "missing_required_sections": required_keys,
        "reason_of_adding_required_sections": [],
        "missing_good_to_have_sections": good_to_have_keys,
        "reason_of_adding_good_to_have_sections": []
    }

    # Get the sections and reasons for missing required and good to have keys
    for missing_required_section in required_keys:
        sections_and_reasons_result["reason_of_adding_required_sections"].append(
            random.choice(llm_prompts.RESUME_ADJUSTMENT_LOGIC_1_INCLUDED_RECOMMENDATIONS[missing_required_section])
        )
    for missing_good_to_have_section in good_to_have_keys:
        sections_and_reasons_result["reason_of_adding_good_to_have_sections"].append(
            random.choice(llm_prompts.RESUME_ADJUSTMENT_LOGIC_1_INCLUDED_RECOMMENDATIONS[missing_good_to_have_section])
        )
    return sections_and_reasons_result

async def update_or_create_application_kit(data: dict) -> dict:
    """
    Update an existing application kit or create a new one if it doesn't exist.
    Also updates the user's profession based on resume analysis.

    Args:
        data: A dictionary containing at least the jd_id and other optional fields
            like resume_id, user_id, job_insight_id, etc.

    Returns:
        The updated or created application kit as a dictionary
    """
    try:
        # Ensure jd_id is provided
        if "jd_id" not in data or "resume_id" not in data or "user_id" not in data:
            return json_response(Response(
                error=ResponseCode.SERVER_ERROR,
                message="Error",
                data={"error": "jd_id, resume_id and user_id are required to update or create an application kit"}
            ), status_code=403)

        # Check if there's an existing application kit for this job description
        query_params = {k: v for k, v in data.items() if k in ["jd_id", "resume_id", "user_id"]}
        app_kit_query = ApplicationKitQuery(
            **query_params,
            order_by=["-created_at"]
        )
        pagination = Pagination(page=1, page_size=1)
        app_kits, total = await ApplicationKitRepository.get_list(app_kit_query, pagination)

        result_kit = None
        if app_kits and total > 0:
            # Update the existing application kit
            app_kit_id = app_kits[0]["id"]
            logger.info(f"Updating application kit ID {app_kit_id}")
            _, updated_app_kit = await ApplicationKitRepository.update(app_kit_id, data)
            logger.info(f"Updated application kit {app_kit_id}")
            result_kit = updated_app_kit
        else:
            # Create a new application kit
            logger.info(f"Creating new application kit")
            new_app_kit = await ApplicationKitRepository.create_one(data)
            logger.info(f"Created new application kit with ID {new_app_kit['id']}")
            result_kit = new_app_kit

        # After creating/updating application kit, analyze and update user profession
        if result_kit and result_kit.get('id'):
            try:
                # Detect professions using the existing function
                profession = await detect_professions(data['resume_id'])

                # Update user's profession field
                # if isinstance(profession, list) and len(profession) > 0:
                #     await UserRepository.update(data['user_id'], {
                #         "profession": profession
                #     })
                #     logger.info(f"Updated user {data['user_id']} profession to {profession}")
                # else:
                #     logger.info(f"No profession detected for user {data['user_id']}")

            except Exception as e:
                logger.error(f"Error updating user profession: {str(e)}")
                # Continue even if profession update fails
                pass

        return result_kit
    except Exception as e:
        logger.error(f"Failed to update or create application kit: {str(e)}")
        raise
async def detect_professions(resume_id: str) -> list[str]:
    """
    Detects professions based on a resume and updates the user's profession field.

    Args:
        resume_id (str): The ID of the resume to analyze

    Returns:
        list[str]: List of detected professions

    Raises:
        Exception: If there's an error during profession detection
    """
    try:
        # Get resume object to extract user_id
        resume_query = ResumeQuery(id=resume_id)
        resume = await ResumeRepository.get_one(resume_query)
        if not resume:
            logger.error(f"Resume with ID {resume_id} not found")
            return []

        # Extract user_id from the resume
        user_id = resume.get("user_id")
        if not user_id:
            logger.error(f"User ID not found in resume {resume_id}")
            return []

        # Get resume content
        resume_details = await get_filtered_resume_content(resume_id)

        # Initialize Groq client
        client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))
        GROQ_MODEL = get_available_groq_model()

        # Prepare system message for profession analysis
        messages = [
            {
                "role": "system",
                "content": """You are an expert at analyzing resumes and categorizing professions.
Use your reasoning to analyze the resume and you are responsible to determine the user's profession. Focus on less than 3 professions highest correlated with the resume.
E.g. the profession 'Marketing Lead' if it shows in the resume headline, 'Backend Developer' if user experience reflects the tech stack related to Backend.
If the profession doesn't clearly fit into the main categories, use ['Other']. Returning array of multiple professions is allowed.
Return the response in JSON format.
{
    "profession": ["List of the Profession, default: Other"]
}"""
            },
            {
                "role": "user",
                "content": f"Based on this resume content, determine the professional positions that users are applying to: {resume_details}"
            }
        ]

        # Get profession analysis from Groq
        completion = client_groq.chat.completions.create(
            model=GROQ_MODEL,
            messages=messages,
            temperature=0.2,
            seed=4,
            response_format={"type": "json_object"}
        )

        profession_info = json.loads(completion.choices[0].message.content)

        profession = profession_info.get("profession", [])
        if profession == [] or profession == ["Other"]:
            profession = []

        # Handle if profession is a string instead of a list
        if isinstance(profession, str):
            profession = [profession]

        logger.info(f"Detected professions for resume {resume_id}: {profession}")

        # Update user's profession field
        if isinstance(profession, list) and len(profession) > 0:
            await UserRepository.update(user_id, {"profession": profession})
            logger.info(f"Updated user {user_id} with professions: {profession}")
        else:
            logger.info(f"No profession detected for user {user_id}")

        return profession

    except Exception as e:
        logger.error(f"Error detecting professions from resume {resume_id}: {str(e)}")
        return []

def get_job_description_from_url(url: str) -> str:
    """
    Fetch and process job description from a given URL using Firecrawl API.
    Falls back to LinkedIn-specific scraping for LinkedIn URLs or when Firecrawl fails.
    
    Args:
        url (str): The URL to fetch job description from
        
    Returns:
        tuple[str, str]: Processed job description content and HTML content
        
    Raises:
        ValueError: If both FIRECRAWL_API_KEY and JINA_API_KEY are not set
        requests.exceptions.RequestException: If both methods fail
    """
    # Check if this is a LinkedIn URL and use the specialized function directly
    try:
        parsed_url = urlparse(url)
        linkedin_domains = ['linkedin.com', 'www.linkedin.com']
        if parsed_url.netloc.lower() in linkedin_domains and '/jobs/' in parsed_url.path.lower():
            logger.info(f"Detected LinkedIn URL, using LinkedIn-specific scraper: {url}")
            content = get_job_description_from_linkedin(url)
            return content
    except Exception as e:
        logger.warning(f"LinkedIn-specific scraping failed, falling back to Firecrawl: {str(e)}")
    
    # Try Firecrawl API first
    try:
        # Get API key from environment variable
        FIRECRAWL_API_KEY = os.getenv("FIRECRAWL_API_KEY")
        
        if not FIRECRAWL_API_KEY:
            raise ValueError("FIRECRAWL_API_KEY environment variable is not set")
        
        base_url = "https://api.firecrawl.dev/v1/scrape"
        
        payload = {
            "url": url,
            "formats": ["markdown", "html"],
            "onlyMainContent": True,
            "includeTags": [],
            "excludeTags": [],
            "headers": {},
            "waitFor": 0,
            "mobile": False,
            "skipTlsVerification": False,
            "timeout": 30000,
            "actions": [
                {
                    "type": "wait",
                    "milliseconds": 2000
                }
            ],
            "location": {
                "country": "US",
                "languages": ["en-US"]
            },
            "removeBase64Images": True,
            "blockAds": True,
            "proxy": "auto"
        }
        
        headers = {
            "Authorization": f"Bearer {FIRECRAWL_API_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(base_url, json=payload, headers=headers, timeout=30)
        response.raise_for_status()
        response_json = response.json()
        content = response_json.get('data', {}).get('markdown', '')
        
        # Remove lines containing hyperlinks and unrelated content
        content_lines = content.split('\n')
        filtered_lines = []
        for line in content_lines:
            # Skip lines containing hyperlinks
            if 'http' in line.lower() or 'www.' in line.lower():
                continue
            if not line.strip():
                continue
            filtered_lines.append(line)
        
        return '\n'.join(filtered_lines)
        
    except Exception as e:
        logger.warning(f"Firecrawl API failed: {str(e)}, attempting LinkedIn fallback")
        
        # Fallback to LinkedIn scraper if Firecrawl fails
        try:
            content = get_job_description_from_linkedin(url)
            return content
        except Exception as linkedin_error:
            logger.error(f"Both Firecrawl and LinkedIn scraping failed. Firecrawl error: {str(e)}, LinkedIn error: {str(linkedin_error)}")
            raise Exception(f"Both scraping methods failed. Firecrawl: {str(e)}, LinkedIn: {str(linkedin_error)}")

def get_job_description_from_linkedin(url: str) -> str:
    """
    Fetch and process job description from a LinkedIn URL using Jina.ai API.
    
    Args:
        url (str): The LinkedIn URL to fetch job description from
        
    Returns:
        str: Processed job description content in markdown format
        
    Raises:
        ValueError: If URL is invalid, not a LinkedIn URL, or JINA_API_KEY is not set
        requests.exceptions.RequestException: If the request fails
    """
    # Validate URL format
    try:
        parsed_url = urlparse(url)
        if not all([parsed_url.scheme, parsed_url.netloc]):
            raise ValueError(f"Invalid URL format: {url}")
    except Exception:
        raise ValueError(f"Invalid URL format: {url}")
    
    # Check if it's a LinkedIn URL
    linkedin_domains = ['linkedin.com', 'www.linkedin.com']
    if parsed_url.netloc.lower() not in linkedin_domains:
        raise ValueError(f"URL must be from LinkedIn. Provided: {parsed_url.netloc}")
    
    # Check if it's a job posting URL
    if '/jobs/' not in parsed_url.path.lower():
        raise ValueError(f"URL must be a LinkedIn job posting (should contain '/jobs/'). Provided path: {parsed_url.path}")
    
    # Get API key from environment variable
    JINA_API_KEY = os.getenv("JINA_API_KEY")
    
    if not JINA_API_KEY:
        raise ValueError("JINA_API_KEY environment variable is not set")
    
    # Construct the jina.ai URL
    jina_url = f"https://r.jina.ai/{url}"
    
    headers = {
        "Accept": "application/json",
        "Authorization": f"Bearer {JINA_API_KEY}",
        "X-Engine": "browser",
        "X-Return-Format": "markdown"
    }
    
    try:
        response = requests.get(jina_url, headers=headers, timeout=30)
        response.raise_for_status()
        result = response.json()
        jd_markdown = result["data"]['content']
        
        # Return the text content
        return jd_markdown
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching job description from LinkedIn URL {url}: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing LinkedIn URL {url}: {str(e)}")
        raise


